# File Explorer Enhancement Technical Specification

## Overview

This document provides detailed technical specifications for implementing advanced visual features and context menu functionality in the file explorer. All implementations must follow User Guidelines with no mock data, placeholders, or test content.

## Architecture Overview

### Component Hierarchy
```
file-explorer/components/file-sidebar/
├── index.ts                    (EXISTING - entry point)
├── SidebarItem.tsx            (ENHANCE - add context menu & error states)
├── CodeFileIcon.tsx           (ENHANCE - advanced icon system)
├── icons/                     (NEW - icon system)
│   ├── FileTypeIcons.tsx
│   ├── IconThemes.ts
│   ├── IconCache.ts
│   └── svg/
├── context-menu/              (NEW - context menu system)
│   ├── FileContextMenu.tsx
│   ├── FolderContextMenu.tsx
│   ├── ContextMenuActions.ts
│   └── ClipboardManager.ts
└── error-indicators/          (NEW - error visualization)
    ├── ErrorBadge.tsx
    ├── ErrorStateManager.ts
    └── FileErrorTracker.ts
```

## Phase 1: Advanced File Type Icons

### 1.1 Enhanced Icon System

#### FileTypeIcons.tsx
```typescript
export interface FileTypeIconProps {
  extension: string;
  size?: 'sm' | 'md' | 'lg';
  theme?: 'light' | 'dark' | 'auto';
  variant?: 'default' | 'outline' | 'filled';
  className?: string;
  showTooltip?: boolean;
}

export interface IconDefinition {
  component: React.ComponentType<any>;
  color: string;
  backgroundColor: string;
  category: 'language' | 'framework' | 'data' | 'media' | 'document';
  priority: number;
}

export const FileTypeIcons: React.FC<FileTypeIconProps> = ({
  extension,
  size = 'md',
  theme = 'auto',
  variant = 'default',
  className,
  showTooltip = false
}) => {
  // Implementation with comprehensive file type support
};
```

#### IconMapping.ts
```typescript
export const FILE_TYPE_MAPPINGS: Record<string, IconDefinition> = {
  // Programming Languages
  'js': {
    component: JavaScriptIcon,
    color: '#F7DF1E',
    backgroundColor: '#F7DF1E15',
    category: 'language',
    priority: 1
  },
  'ts': {
    component: TypeScriptIcon,
    color: '#3178C6',
    backgroundColor: '#3178C615',
    category: 'language',
    priority: 1
  },
  // ... 50+ file types
};

export const ICON_THEMES = {
  light: { /* light theme colors */ },
  dark: { /* dark theme colors */ },
  'high-contrast': { /* accessibility colors */ }
};
```

#### IconCache.ts
```typescript
export class IconCache {
  private cache = new Map<string, React.ComponentType>();
  private preloadedTypes = new Set<string>();

  public getIcon(extension: string, options: IconOptions): React.ComponentType {
    const cacheKey = this.generateCacheKey(extension, options);
    
    if (this.cache.has(cacheKey)) {
      return this.cache.get(cacheKey)!;
    }

    const icon = this.createIcon(extension, options);
    this.cache.set(cacheKey, icon);
    return icon;
  }

  public preloadCommonIcons(): void {
    const commonTypes = ['js', 'ts', 'tsx', 'jsx', 'py', 'json', 'md'];
    commonTypes.forEach(type => this.preloadIcon(type));
  }
}
```

### 1.2 Performance Optimization

#### Lazy Loading Strategy
```typescript
const LazyFileIcon = React.lazy(() => import('./icons/FileTypeIcons'));

export const OptimizedFileIcon: React.FC<FileTypeIconProps> = (props) => {
  return (
    <Suspense fallback={<DefaultIcon />}>
      <LazyFileIcon {...props} />
    </Suspense>
  );
};
```

#### Memory Management
```typescript
export const useIconCache = () => {
  const cacheRef = useRef(new IconCache());
  
  useEffect(() => {
    // Preload common icons
    cacheRef.current.preloadCommonIcons();
    
    // Cleanup on unmount
    return () => {
      cacheRef.current.clear();
    };
  }, []);

  return cacheRef.current;
};
```

## Phase 2: Context Menu System

### 2.1 Context Menu Components

#### FileContextMenu.tsx
```typescript
export interface FileContextMenuProps {
  file: FileSystemItem;
  onAction: (action: ContextMenuAction, file: FileSystemItem) => void;
  position: { x: number; y: number };
  onClose: () => void;
}

export const FileContextMenu: React.FC<FileContextMenuProps> = ({
  file,
  onAction,
  position,
  onClose
}) => {
  const menuItems = useMemo(() => [
    { id: 'open', label: 'Open', icon: FileOpen, shortcut: 'Enter' },
    { id: 'open-with', label: 'Open With...', icon: ExternalLink },
    { type: 'separator' },
    { id: 'copy', label: 'Copy', icon: Copy, shortcut: 'Ctrl+C' },
    { id: 'cut', label: 'Cut', icon: Scissors, shortcut: 'Ctrl+X' },
    { id: 'paste', label: 'Paste', icon: Clipboard, shortcut: 'Ctrl+V' },
    { type: 'separator' },
    { id: 'rename', label: 'Rename', icon: Edit, shortcut: 'F2' },
    { id: 'delete', label: 'Delete', icon: Trash, shortcut: 'Delete' },
    { type: 'separator' },
    { id: 'copy-path', label: 'Copy Path', icon: Link },
    { id: 'copy-relative-path', label: 'Copy Relative Path', icon: Link },
    { id: 'reveal', label: 'Reveal in Explorer', icon: FolderOpen },
    { type: 'separator' },
    { id: 'properties', label: 'Properties', icon: Settings }
  ], []);

  return (
    <ContextMenu>
      <ContextMenuContent 
        style={{ left: position.x, top: position.y }}
        onEscapeKeyDown={onClose}
      >
        {menuItems.map((item, index) => (
          item.type === 'separator' ? (
            <ContextMenuSeparator key={index} />
          ) : (
            <ContextMenuItem
              key={item.id}
              onClick={() => onAction(item.id as ContextMenuAction, file)}
            >
              <item.icon className="h-4 w-4 mr-2" />
              {item.label}
              {item.shortcut && (
                <ContextMenuShortcut>{item.shortcut}</ContextMenuShortcut>
              )}
            </ContextMenuItem>
          )
        ))}
      </ContextMenuContent>
    </ContextMenu>
  );
};
```

#### ContextMenuActions.ts
```typescript
export type ContextMenuAction = 
  | 'open' | 'open-with' | 'copy' | 'cut' | 'paste' 
  | 'rename' | 'delete' | 'duplicate' | 'copy-path' 
  | 'copy-relative-path' | 'reveal' | 'properties'
  | 'new-file' | 'new-folder' | 'open-terminal';

export class ContextMenuActionHandler {
  constructor(
    private fileOperations: FileOperationsManager,
    private clipboardManager: ClipboardManager
  ) {}

  async handleAction(
    action: ContextMenuAction, 
    item: FileSystemItem
  ): Promise<void> {
    switch (action) {
      case 'copy':
        await this.clipboardManager.copyFile(item);
        break;
      case 'cut':
        await this.clipboardManager.cutFile(item);
        break;
      case 'paste':
        await this.clipboardManager.paste(item.path);
        break;
      case 'rename':
        await this.handleRename(item);
        break;
      case 'delete':
        await this.handleDelete(item);
        break;
      case 'copy-path':
        await this.clipboardManager.copyPath(item.path);
        break;
      case 'reveal':
        await this.revealInExplorer(item.path);
        break;
      // ... other actions
    }
  }

  private async handleRename(item: FileSystemItem): Promise<void> {
    // Implementation with inline editing or dialog
  }

  private async handleDelete(item: FileSystemItem): Promise<void> {
    // Implementation with confirmation dialog
  }
}
```

#### ClipboardManager.ts
```typescript
export interface ClipboardItem {
  type: 'file' | 'folder';
  path: string;
  operation: 'copy' | 'cut';
  timestamp: number;
}

export class ClipboardManager {
  private clipboard: ClipboardItem[] = [];

  async copyFile(item: FileSystemItem): Promise<void> {
    this.clipboard = [{
      type: item.type === 'folder' ? 'folder' : 'file',
      path: item.path,
      operation: 'copy',
      timestamp: Date.now()
    }];

    // Also copy to system clipboard
    if (navigator.clipboard) {
      await navigator.clipboard.writeText(item.path);
    }
  }

  async cutFile(item: FileSystemItem): Promise<void> {
    this.clipboard = [{
      type: item.type === 'folder' ? 'folder' : 'file',
      path: item.path,
      operation: 'cut',
      timestamp: Date.now()
    }];
  }

  async paste(targetPath: string): Promise<void> {
    for (const item of this.clipboard) {
      if (item.operation === 'copy') {
        await this.copyItem(item, targetPath);
      } else if (item.operation === 'cut') {
        await this.moveItem(item, targetPath);
      }
    }

    // Clear clipboard after paste
    this.clipboard = [];
  }

  private async copyItem(item: ClipboardItem, targetPath: string): Promise<void> {
    // Implementation using FileOperationsManager
  }

  private async moveItem(item: ClipboardItem, targetPath: string): Promise<void> {
    // Implementation using FileOperationsManager
  }
}
```

### 2.2 Right-Click Integration

#### Enhanced SidebarItem.tsx
```typescript
export const SidebarItem: React.FC<SidebarItemProps> = ({
  item,
  level = 0,
  onToggle,
  onSelect,
  selectedFile,
  showFileIcons = true
}) => {
  const [contextMenu, setContextMenu] = useState<{
    show: boolean;
    position: { x: number; y: number };
  }>({ show: false, position: { x: 0, y: 0 } });

  const handleContextMenu = useCallback((e: React.MouseEvent) => {
    e.preventDefault();
    e.stopPropagation();
    
    setContextMenu({
      show: true,
      position: { x: e.clientX, y: e.clientY }
    });
  }, []);

  const handleContextMenuAction = useCallback(async (
    action: ContextMenuAction,
    file: FileSystemItem
  ) => {
    const actionHandler = new ContextMenuActionHandler(
      globalFileOperations,
      new ClipboardManager()
    );
    
    await actionHandler.handleAction(action, file);
    setContextMenu({ show: false, position: { x: 0, y: 0 } });
  }, []);

  return (
    <>
      <div
        className={cn(
          "flex items-center py-1 px-2 text-sm cursor-pointer hover:bg-accent group",
          isSelected && "bg-accent text-accent-foreground"
        )}
        style={{ paddingLeft: `${indent + 8}px` }}
        onClick={handleClick}
        onContextMenu={handleContextMenu}
      >
        {/* Existing content */}
      </div>

      {contextMenu.show && (
        <Portal>
          {isFolder ? (
            <FolderContextMenu
              folder={item}
              onAction={handleContextMenuAction}
              position={contextMenu.position}
              onClose={() => setContextMenu({ show: false, position: { x: 0, y: 0 } })}
            />
          ) : (
            <FileContextMenu
              file={item}
              onAction={handleContextMenuAction}
              position={contextMenu.position}
              onClose={() => setContextMenu({ show: false, position: { x: 0, y: 0 } })}
            />
          )}
        </Portal>
      )}
    </>
  );
};
```

## Phase 3: Color Coding Integration

### 3.1 Error State Management

#### ErrorStateManager.ts
```typescript
export interface FileErrorState {
  filePath: string;
  hasErrors: boolean;
  errorTypes: ErrorType[];
  errorCount: number;
  warningCount: number;
  lastUpdated: number;
  severity: 'error' | 'warning' | 'info';
}

export type ErrorType = 'syntax' | 'type' | 'lint' | 'build' | 'git';

export class ErrorStateManager {
  private errorStates = new Map<string, FileErrorState>();
  private subscribers = new Set<(state: FileErrorState) => void>();

  constructor(private errorDetector: ErrorDetector) {
    this.setupErrorDetection();
  }

  private setupErrorDetection(): void {
    this.errorDetector.onErrorDetected((result: ErrorDetectionResult) => {
      this.updateFileErrorState(result);
    });
  }

  private updateFileErrorState(result: ErrorDetectionResult): void {
    const errorState: FileErrorState = {
      filePath: result.filePath,
      hasErrors: result.errors.length > 0 || result.warnings.length > 0,
      errorTypes: this.categorizeErrors(result.errors),
      errorCount: result.errors.length,
      warningCount: result.warnings.length,
      lastUpdated: Date.now(),
      severity: result.errors.length > 0 ? 'error' : 'warning'
    };

    this.errorStates.set(result.filePath, errorState);
    this.notifySubscribers(errorState);
  }

  public getErrorState(filePath: string): FileErrorState | null {
    return this.errorStates.get(filePath) || null;
  }

  public subscribe(callback: (state: FileErrorState) => void): () => void {
    this.subscribers.add(callback);
    return () => this.subscribers.delete(callback);
  }
}
```

#### ErrorBadge.tsx
```typescript
export interface ErrorBadgeProps {
  errorState: FileErrorState;
  size?: 'sm' | 'md';
  position?: 'inline' | 'overlay';
}

export const ErrorBadge: React.FC<ErrorBadgeProps> = ({
  errorState,
  size = 'sm',
  position = 'inline'
}) => {
  if (!errorState.hasErrors) return null;

  const getErrorColor = () => {
    switch (errorState.severity) {
      case 'error': return 'bg-red-500';
      case 'warning': return 'bg-yellow-500';
      default: return 'bg-blue-500';
    }
  };

  const getErrorIcon = () => {
    switch (errorState.severity) {
      case 'error': return <XCircle className="h-3 w-3" />;
      case 'warning': return <AlertTriangle className="h-3 w-3" />;
      default: return <Info className="h-3 w-3" />;
    }
  };

  return (
    <Tooltip>
      <TooltipTrigger>
        <Badge 
          variant="destructive" 
          className={cn(
            getErrorColor(),
            size === 'sm' ? 'h-4 w-4 p-0' : 'h-5 w-5 p-0',
            position === 'overlay' && 'absolute -top-1 -right-1'
          )}
        >
          {getErrorIcon()}
        </Badge>
      </TooltipTrigger>
      <TooltipContent>
        <div className="text-xs">
          {errorState.errorCount > 0 && (
            <div>{errorState.errorCount} error(s)</div>
          )}
          {errorState.warningCount > 0 && (
            <div>{errorState.warningCount} warning(s)</div>
          )}
        </div>
      </TooltipContent>
    </Tooltip>
  );
};
```

### 3.2 Integration with File Tree

#### Enhanced SidebarItem with Error States
```typescript
export const SidebarItem: React.FC<SidebarItemProps> = (props) => {
  const [errorState, setErrorState] = useState<FileErrorState | null>(null);
  const errorStateManager = useErrorStateManager();

  useEffect(() => {
    if (props.item.path) {
      const unsubscribe = errorStateManager.subscribe((state) => {
        if (state.filePath === props.item.path) {
          setErrorState(state);
        }
      });

      // Get initial state
      const initialState = errorStateManager.getErrorState(props.item.path);
      setErrorState(initialState);

      return unsubscribe;
    }
  }, [props.item.path, errorStateManager]);

  const getItemClassName = () => {
    let baseClass = "flex items-center py-1 px-2 text-sm cursor-pointer hover:bg-accent group";
    
    if (errorState?.hasErrors) {
      switch (errorState.severity) {
        case 'error':
          baseClass += " border-l-2 border-red-500 bg-red-50 dark:bg-red-950";
          break;
        case 'warning':
          baseClass += " border-l-2 border-yellow-500 bg-yellow-50 dark:bg-yellow-950";
          break;
      }
    }

    return baseClass;
  };

  return (
    <div className={getItemClassName()}>
      {/* Existing content */}
      
      {/* Error indicator */}
      {errorState && (
        <div className="ml-auto">
          <ErrorBadge errorState={errorState} size="sm" />
        </div>
      )}
    </div>
  );
};
```

## Integration Points

### File Operations Integration
- Connect context menu actions with `FileOperationsManager`
- Ensure all file operations trigger appropriate UI updates
- Implement proper error handling and user feedback

### Error Detection Integration
- Connect with existing `ErrorDetector` system
- Implement efficient error state polling
- Add file-level error aggregation for folders

### Performance Considerations
- Implement efficient caching for icons and error states
- Use React.memo and useMemo for optimization
- Debounce error detection updates
- Lazy load context menu components

---

**Document Version**: 1.0  
**Last Updated**: 2025-06-18  
**Status**: Implementation Ready  
**Compliance**: User Guidelines Compliant (No Mock Data)
