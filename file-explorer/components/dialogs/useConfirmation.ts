// useConfirmation.ts - Hook for easy confirmation dialogs

"use client"

import { useState, useCallback } from 'react';

export interface ConfirmationOptions {
  title: string;
  description: string;
  confirmText?: string;
  cancelText?: string;
  variant?: 'default' | 'destructive' | 'warning' | 'info';
}

export interface ConfirmationState {
  isOpen: boolean;
  options: ConfirmationOptions | null;
  resolve: ((value: boolean) => void) | null;
}

export const useConfirmation = () => {
  const [state, setState] = useState<ConfirmationState>({
    isOpen: false,
    options: null,
    resolve: null
  });

  const confirm = useCallback((options: ConfirmationOptions): Promise<boolean> => {
    return new Promise((resolve) => {
      setState({
        isOpen: true,
        options,
        resolve
      });
    });
  }, []);

  const handleConfirm = useCallback(() => {
    if (state.resolve) {
      state.resolve(true);
    }
    setState({
      isOpen: false,
      options: null,
      resolve: null
    });
  }, [state.resolve]);

  const handleCancel = useCallback(() => {
    if (state.resolve) {
      state.resolve(false);
    }
    setState({
      isOpen: false,
      options: null,
      resolve: null
    });
  }, [state.resolve]);

  return {
    confirm,
    confirmationProps: {
      isOpen: state.isOpen,
      onClose: handleCancel,
      onConfirm: handleConfirm,
      title: state.options?.title || '',
      description: state.options?.description || '',
      confirmText: state.options?.confirmText,
      cancelText: state.options?.cancelText,
      variant: state.options?.variant
    }
  };
};

export default useConfirmation;
