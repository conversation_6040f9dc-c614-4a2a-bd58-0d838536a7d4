// components/agents/agent-manager/file-operations.ts

import { agentIPCBridge } from '@/lib/agent-ipc-bridge';
import { logTaskExecutionSkipped } from '../../background/logger';

export interface IFileOperations {
  agentCreateFile(agentId: string, path: string, content?: string): Promise<boolean>;
  agentWriteFile(agentId: string, path: string, content: string): Promise<boolean>;
  agentDeleteFile(agentId: string, path: string): Promise<boolean>;
  agentCreateDirectory(agentId: string, path: string): Promise<boolean>;
  agentExecuteCommand(agentId: string, command: string, cwd?: string): Promise<{ success: boolean; output?: string; error?: string }>;
}

export class FileOperations implements IFileOperations {
  private fileOperations: any;

  constructor() {
    // Initialize file operations service
    this.initializeFileOperations();
  }

  async agentCreateFile(agentId: string, path: string, content: string = ''): Promise<boolean> {
    try {
      // Enforce project context requirement
      const { activeProjectService } = await import('../../services/active-project-service');
      if (!activeProjectService.getActiveProjectPath()) {
        const errorMessage = "Agent execution blocked: no active project selected. Please open or create a project first.";
        console.error(`Agent ${agentId}: ${errorMessage}`);

        // ✅ MANDATORY UPGRADE: Log agent execution blocked due to no active project
        logTaskExecutionSkipped(agentId, {
          taskId: `file-create-${Date.now()}`,
          cause: 'no_active_project',
          reason: 'Agent file creation blocked - no active project selected. User must open or create a project first.',
          contextMissing: true,
          invalidTask: false
        });

        // Show user-visible error
        this.showUserError(errorMessage);
        throw new Error(errorMessage);
      }

      // Resolve path using active project service if it's a relative path
      let resolvedPath = path;
      if (!path.startsWith('/')) {
        try {
          resolvedPath = activeProjectService.resolve(path);
          console.log(`Agent ${agentId}: Resolved relative path '${path}' to '${resolvedPath}'`);
        } catch (error) {
          console.error(`Agent ${agentId}: Failed to resolve path '${path}':`, error);
          throw new Error(`Cannot resolve file path '${path}': ${error instanceof Error ? error.message : 'Unknown error'}`);
        }
      }

      const result = await this.fileOperations.createFile(resolvedPath, content, {
        backup: true,
        overwrite: false
      }, agentId);

      if (result.success) {
        // Broadcast agent action via IPC
        await agentIPCBridge.addMessage({
          agentId,
          message: `Created file: ${resolvedPath}`,
          timestamp: Date.now(),
          type: 'info'
        });

        // Trigger file explorer refresh across all windows
        if (typeof window !== 'undefined' && window.electronAPI && window.electronAPI.ipc) {
          window.electronAPI.ipc.send('file-system-changed', { type: 'create', path: resolvedPath });
        }

        return true;
      }
      return false;
    } catch (error) {
      console.error(`Agent ${agentId} failed to create file ${path}:`, error);
      return false;
    }
  }

  async agentWriteFile(agentId: string, path: string, content: string): Promise<boolean> {
    try {
      const result = await this.fileOperations.writeFile(path, content, {
        backup: true
      }, agentId);

      if (result.success) {
        // Broadcast agent action via IPC
        await agentIPCBridge.addMessage({
          agentId,
          message: `Modified file: ${path}`,
          timestamp: Date.now(),
          type: 'info'
        });

        // Trigger file explorer refresh across all windows
        if (typeof window !== 'undefined' && window.electronAPI && window.electronAPI.ipc) {
          window.electronAPI.ipc.send('file-system-changed', { type: 'modify', path });
        }

        return true;
      }
      return false;
    } catch (error) {
      console.error(`Agent ${agentId} failed to write file ${path}:`, error);
      return false;
    }
  }

  async agentDeleteFile(agentId: string, path: string): Promise<boolean> {
    try {
      const result = await this.fileOperations.deleteFile(path, {
        backup: true
      }, agentId);

      if (result.success) {
        // Broadcast agent action via IPC
        await agentIPCBridge.addMessage({
          agentId,
          message: `Deleted file: ${path}`,
          timestamp: Date.now(),
          type: 'info'
        });

        // Trigger file explorer refresh across all windows
        if (typeof window !== 'undefined' && window.electronAPI && window.electronAPI.ipc) {
          window.electronAPI.ipc.send('file-system-changed', { type: 'delete', path });
        }

        return true;
      }
      return false;
    } catch (error) {
      console.error(`Agent ${agentId} failed to delete file ${path}:`, error);
      return false;
    }
  }

  async agentCreateDirectory(agentId: string, path: string): Promise<boolean> {
    try {
      // Enforce project context requirement
      const { activeProjectService } = await import('../../services/active-project-service');
      if (!activeProjectService.getActiveProjectPath()) {
        const errorMessage = "Agent execution blocked: no active project selected. Please open or create a project first.";
        console.error(`Agent ${agentId}: ${errorMessage}`);

        // ✅ MANDATORY UPGRADE: Log agent execution blocked due to no active project
        logTaskExecutionSkipped(agentId, {
          taskId: `dir-create-${Date.now()}`,
          cause: 'no_active_project',
          reason: 'Agent directory creation blocked - no active project selected. User must open or create a project first.',
          contextMissing: true,
          invalidTask: false
        });

        // Show user-visible error
        this.showUserError(errorMessage);
        throw new Error(errorMessage);
      }

      // Resolve path using active project service if it's a relative path
      let resolvedPath = path;
      if (!path.startsWith('/')) {
        try {
          resolvedPath = activeProjectService.resolve(path);
          console.log(`Agent ${agentId}: Resolved relative directory path '${path}' to '${resolvedPath}'`);
        } catch (error) {
          console.error(`Agent ${agentId}: Failed to resolve directory path '${path}':`, error);
          throw new Error(`Cannot resolve directory path '${path}': ${error instanceof Error ? error.message : 'Unknown error'}`);
        }
      }

      const result = await this.fileOperations.createDirectory(resolvedPath, {}, agentId);

      if (result.success) {
        // Broadcast agent action via IPC
        await agentIPCBridge.addMessage({
          agentId,
          message: `Created directory: ${resolvedPath}`,
          timestamp: Date.now(),
          type: 'info'
        });

        // Trigger file explorer refresh across all windows
        if (typeof window !== 'undefined' && window.electronAPI && window.electronAPI.ipc) {
          window.electronAPI.ipc.send('file-system-changed', { type: 'create', path: resolvedPath });
        }

        return true;
      }
      return false;
    } catch (error) {
      console.error(`Agent ${agentId} failed to create directory ${path}:`, error);
      return false;
    }
  }

  async agentExecuteCommand(agentId: string, command: string, cwd?: string): Promise<{ success: boolean; output?: string; error?: string }> {
    try {
      // Broadcast command execution start
      await agentIPCBridge.addMessage({
        agentId,
        message: `Executing command: ${command}`,
        timestamp: Date.now(),
        type: 'info'
      });

      // Execute command via Electron API if available
      if (typeof window !== 'undefined' && window.electronAPI && window.electronAPI.executeCommand) {
        const result = await window.electronAPI.executeCommand(command, cwd);

        // Broadcast command result
        await agentIPCBridge.addMessage({
          agentId,
          message: `Command completed: ${command} (${result.success ? 'success' : 'failed'})`,
          timestamp: Date.now(),
          type: result.success ? 'success' : 'error'
        });

        return result;
      } else {
        // Fallback for non-Electron environments
        console.log(`Agent ${agentId} would execute: ${command}`);
        return { success: true, output: `Simulated execution of: ${command}` };
      }
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : String(error);

      // Broadcast command error
      await agentIPCBridge.addMessage({
        agentId,
        message: `Command failed: ${command} - ${errorMessage}`,
        timestamp: Date.now(),
        type: 'error'
      });

      return { success: false, error: errorMessage };
    }
  }

  private async initializeFileOperations(): Promise<void> {
    try {
      // Import file operations service
      const { fileOperations } = await import('../../background/file-operations');
      this.fileOperations = fileOperations;
      console.log('File operations service initialized');
    } catch (error) {
      console.error('Failed to initialize file operations service:', error);
      // Create a mock file operations service for fallback
      this.fileOperations = this.createMockFileOperations();
    }
  }

  private createMockFileOperations(): any {
    return {
      createFile: async (path: string, content: string, options: any, agentId: string) => {
        console.log(`Mock: Creating file ${path} for agent ${agentId}`);
        return { success: true, path };
      },
      writeFile: async (path: string, content: string, options: any, agentId: string) => {
        console.log(`Mock: Writing file ${path} for agent ${agentId}`);
        return { success: true, path };
      },
      deleteFile: async (path: string, options: any, agentId: string) => {
        console.log(`Mock: Deleting file ${path} for agent ${agentId}`);
        return { success: true, path };
      },
      createDirectory: async (path: string, options: any, agentId: string) => {
        console.log(`Mock: Creating directory ${path} for agent ${agentId}`);
        return { success: true, path };
      }
    };
  }

  private showUserError(message: string): void {
    try {
      // Try to show toast notification if available
      if (typeof window !== 'undefined' && (window as any).showToast) {
        (window as any).showToast({
          title: "Agent Error",
          description: message,
          variant: "destructive",
        });
      } else {
        // Fallback to console error only - no browser alerts
        console.error('Agent Error:', message);

        // Try to dispatch a custom event for error handling
        if (typeof window !== 'undefined') {
          window.dispatchEvent(new CustomEvent('agent-error', {
            detail: { message, title: 'Agent Error' }
          }));
        }
      }
    } catch (error) {
      console.error('Failed to show user error:', error);
    }
  }
}
