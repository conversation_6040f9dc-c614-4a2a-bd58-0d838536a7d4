// components/agents/agent-manager/agent-utils.ts

import { Agent, Task } from '../types';
import { agentIPCBridge } from '@/lib/agent-ipc-bridge';
import { logTaskExecutionSkipped } from '../../background/logger';

export interface IAgentUtils {
  showUserError(message: string): void;
  validateAgentConfiguration(agent: Agent): boolean;
  formatTaskForDisplay(task: Task): string;
  calculateTaskPriority(task: Task): number;
  generateTaskId(): string;
  sanitizeAgentInput(input: string): string;
}

export class AgentUtils implements IAgentUtils {
  /**
   * Show user-visible error message
   */
  showUserError(message: string): void {
    try {
      // Try to show toast notification if available
      if (typeof window !== 'undefined' && (window as any).showToast) {
        (window as any).showToast({
          title: "Agent Error",
          description: message,
          variant: "destructive",
        });
      } else {
        // Fallback to console error only - no browser alerts
        console.error('Agent Error:', message);

        // Try to dispatch a custom event for error handling
        if (typeof window !== 'undefined') {
          window.dispatchEvent(new CustomEvent('agent-error', {
            detail: { message, title: 'Agent Error' }
          }));
        }
      }
    } catch (error) {
      console.error('Failed to show user error:', error);
    }
  }

  validateAgentConfiguration(agent: Agent): boolean {
    try {
      // Check required fields
      if (!agent.id || !agent.name || !agent.role) {
        console.error('Agent missing required fields (id, name, role)');
        return false;
      }

      // Validate agent ID format
      if (!/^[a-zA-Z0-9-_]+$/.test(agent.id)) {
        console.error('Invalid agent ID format');
        return false;
      }

      // Validate role
      const validRoles = ['junior', 'senior', 'micromanager', 'specialist', 'general'];
      if (!validRoles.includes(agent.role)) {
        console.error(`Invalid agent role: ${agent.role}`);
        return false;
      }

      // Validate model configuration
      if (agent.model && !this.validateModelConfiguration(agent.model, agent.provider)) {
        console.error('Invalid model configuration');
        return false;
      }

      // Validate capabilities
      if (agent.capabilities && !Array.isArray(agent.capabilities)) {
        console.error('Agent capabilities must be an array');
        return false;
      }

      // Validate temperature
      if (agent.temperature !== undefined && (agent.temperature < 0 || agent.temperature > 2)) {
        console.error('Agent temperature must be between 0 and 2');
        return false;
      }

      // Validate max tokens
      if (agent.maxTokens !== undefined && (agent.maxTokens < 1 || agent.maxTokens > 100000)) {
        console.error('Agent maxTokens must be between 1 and 100000');
        return false;
      }

      return true;
    } catch (error) {
      console.error('Failed to validate agent configuration:', error);
      return false;
    }
  }

  formatTaskForDisplay(task: Task): string {
    try {
      const priority = task.priority ? `[${task.priority.toUpperCase()}]` : '';
      const type = task.type ? `(${task.type})` : '';
      const status = task.status ? `{${task.status}}` : '';
      
      return `${priority} ${type} ${task.title} ${status}`.trim();
    } catch (error) {
      console.error('Failed to format task for display:', error);
      return task.title || 'Unknown Task';
    }
  }

  calculateTaskPriority(task: Task): number {
    try {
      let priority = 0;

      // Base priority from task
      switch (task.priority) {
        case 'urgent':
          priority += 100;
          break;
        case 'high':
          priority += 75;
          break;
        case 'medium':
          priority += 50;
          break;
        case 'low':
          priority += 25;
          break;
        default:
          priority += 50; // Default to medium
      }

      // Adjust based on task age
      if (task.createdAt) {
        const ageInHours = (Date.now() - new Date(task.createdAt).getTime()) / (1000 * 60 * 60);
        priority += Math.min(ageInHours * 2, 20); // Max 20 points for age
      }

      // Adjust based on dependencies
      if (task.dependencies && task.dependencies.length > 0) {
        priority -= task.dependencies.length * 5; // Reduce priority for dependent tasks
      }

      // Adjust based on estimated effort
      if (task.estimatedEffort) {
        if (task.estimatedEffort === 'low') priority += 10;
        else if (task.estimatedEffort === 'high') priority -= 10;
      }

      return Math.max(0, priority);
    } catch (error) {
      console.error('Failed to calculate task priority:', error);
      return 50; // Default priority
    }
  }

  generateTaskId(): string {
    try {
      const timestamp = Date.now();
      const random = Math.random().toString(36).substr(2, 9);
      return `task-${timestamp}-${random}`;
    } catch (error) {
      console.error('Failed to generate task ID:', error);
      return `task-${Date.now()}`;
    }
  }

  sanitizeAgentInput(input: string): string {
    try {
      if (typeof input !== 'string') {
        return '';
      }

      // Remove potentially dangerous characters
      let sanitized = input
        .replace(/[<>]/g, '') // Remove angle brackets
        .replace(/javascript:/gi, '') // Remove javascript: protocol
        .replace(/on\w+=/gi, '') // Remove event handlers
        .trim();

      // Limit length
      if (sanitized.length > 10000) {
        sanitized = sanitized.substring(0, 10000) + '...';
      }

      return sanitized;
    } catch (error) {
      console.error('Failed to sanitize agent input:', error);
      return '';
    }
  }

  // Additional utility methods

  /**
   * Check if agent is available for task assignment
   */
  isAgentAvailable(agent: Agent): boolean {
    return agent.status === 'idle' || agent.status === 'available';
  }

  /**
   * Get agent display name with status
   */
  getAgentDisplayName(agent: Agent): string {
    const statusIcon = this.getStatusIcon(agent.status);
    return `${statusIcon} ${agent.name} (${agent.role})`;
  }

  /**
   * Get status icon for agent
   */
  getStatusIcon(status: string): string {
    switch (status) {
      case 'idle':
        return '🟢';
      case 'busy':
        return '🔴';
      case 'available':
        return '🟡';
      case 'error':
        return '❌';
      case 'offline':
        return '⚫';
      default:
        return '❓';
    }
  }

  /**
   * Format execution time for display
   */
  formatExecutionTime(milliseconds: number): string {
    if (milliseconds < 1000) {
      return `${milliseconds}ms`;
    } else if (milliseconds < 60000) {
      return `${(milliseconds / 1000).toFixed(1)}s`;
    } else {
      const minutes = Math.floor(milliseconds / 60000);
      const seconds = Math.floor((milliseconds % 60000) / 1000);
      return `${minutes}m ${seconds}s`;
    }
  }

  /**
   * Get task type icon
   */
  getTaskTypeIcon(type: string): string {
    switch (type) {
      case 'code-generation':
        return '💻';
      case 'file-operation':
        return '📁';
      case 'analysis':
        return '🔍';
      case 'testing':
        return '🧪';
      case 'documentation':
        return '📝';
      case 'review':
        return '👀';
      default:
        return '📋';
    }
  }

  /**
   * Validate model configuration
   */
  private validateModelConfiguration(model: string, provider?: string): boolean {
    try {
      // Define valid models per provider
      const validModels: Record<string, string[]> = {
        anthropic: [
          'claude-3-opus-20240229',
          'claude-3-sonnet-20240229',
          'claude-3-haiku-20240307'
        ],
        openai: [
          'gpt-4',
          'gpt-4-turbo',
          'gpt-3.5-turbo'
        ],
        perplexity: [
          'llama-3.1-sonar-small-128k-online',
          'llama-3.1-sonar-large-128k-online'
        ]
      };

      if (!provider) {
        // If no provider specified, check if model exists in any provider
        return Object.values(validModels).some(models => models.includes(model));
      }

      // Check if provider exists and model is valid for that provider
      return validModels[provider]?.includes(model) || false;
    } catch (error) {
      console.error('Failed to validate model configuration:', error);
      return false;
    }
  }

  /**
   * Create error context for logging
   */
  createErrorContext(error: Error, context: any = {}): any {
    return {
      errorName: error.name,
      errorMessage: error.message,
      errorStack: error.stack,
      timestamp: new Date().toISOString(),
      context
    };
  }

  /**
   * Deep clone object safely
   */
  deepClone<T>(obj: T): T {
    try {
      return JSON.parse(JSON.stringify(obj));
    } catch (error) {
      console.error('Failed to deep clone object:', error);
      return obj;
    }
  }

  /**
   * Debounce function calls
   */
  debounce<T extends (...args: any[]) => any>(
    func: T,
    wait: number
  ): (...args: Parameters<T>) => void {
    let timeout: NodeJS.Timeout;
    return (...args: Parameters<T>) => {
      clearTimeout(timeout);
      timeout = setTimeout(() => func(...args), wait);
    };
  }

  /**
   * Throttle function calls
   */
  throttle<T extends (...args: any[]) => any>(
    func: T,
    limit: number
  ): (...args: Parameters<T>) => void {
    let inThrottle: boolean;
    return (...args: Parameters<T>) => {
      if (!inThrottle) {
        func(...args);
        inThrottle = true;
        setTimeout(() => inThrottle = false, limit);
      }
    };
  }
}
