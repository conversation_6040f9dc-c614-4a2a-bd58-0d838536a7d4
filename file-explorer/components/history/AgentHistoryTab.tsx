// components/history/AgentHistoryTab.tsx

"use client"

import { useState } from "react"
import { cn } from "@/lib/utils"
import { But<PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Badge } from "@/components/ui/badge"
import { ScrollArea } from "@/components/ui/scroll-area"
import { 
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select"
import { 
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu"
import { 
  Search, 
  Filter, 
  Download, 
  RefreshCw, 
  Trash2, 
  BarChart3,
  Clock,
  CheckCircle,
  XCircle,
  AlertTriangle,
  Loader2
} from "lucide-react"
import { useAgentHistory } from "@/hooks/useAgentHistory"
import HistoryCard from "./HistoryCard"
import type { AgentHistoryFilter } from "@/types/agent-history"
import { AGENT_TYPE_NAMES, STATUS_CONFIG } from "@/types/agent-history"
import { ConfirmationDialog, useConfirmation } from "@/components/dialogs"

export default function AgentHistoryTab() {
  const [searchQuery, setSearchQuery] = useState("")
  const [selectedAgentType, setSelectedAgentType] = useState<string>("all")
  const [selectedStatus, setSelectedStatus] = useState<string>("all")
  const [showStats, setShowStats] = useState(false)
  const { confirm, confirmationProps } = useConfirmation()
  
  const {
    entries,
    stats,
    isLoading,
    error,
    filter,
    setFilter,
    clearFilter,
    refreshHistory,
    clearHistory,
    exportHistory
  } = useAgentHistory()

  // Apply filters
  const handleFilterChange = () => {
    const newFilter: AgentHistoryFilter = {}
    
    if (searchQuery.trim()) {
      newFilter.searchQuery = searchQuery.trim()
    }
    
    if (selectedAgentType !== "all") {
      newFilter.agentTypes = [selectedAgentType]
    }
    
    if (selectedStatus !== "all") {
      newFilter.status = [selectedStatus as any]
    }
    
    setFilter(newFilter)
  }

  // Handle search
  const handleSearch = (value: string) => {
    setSearchQuery(value)
    const newFilter: AgentHistoryFilter = { ...filter }
    
    if (value.trim()) {
      newFilter.searchQuery = value.trim()
    } else {
      delete newFilter.searchQuery
    }
    
    setFilter(newFilter)
  }

  // Handle agent type filter
  const handleAgentTypeChange = (value: string) => {
    setSelectedAgentType(value)
    const newFilter: AgentHistoryFilter = { ...filter }
    
    if (value !== "all") {
      newFilter.agentTypes = [value]
    } else {
      delete newFilter.agentTypes
    }
    
    setFilter(newFilter)
  }

  // Handle status filter
  const handleStatusChange = (value: string) => {
    setSelectedStatus(value)
    const newFilter: AgentHistoryFilter = { ...filter }
    
    if (value !== "all") {
      newFilter.status = [value as any]
    } else {
      delete newFilter.status
    }
    
    setFilter(newFilter)
  }

  // Handle export
  const handleExport = async (format: 'json' | 'csv' | 'markdown') => {
    try {
      const exportData = await exportHistory(format)
      const blob = new Blob([exportData], { 
        type: format === 'json' ? 'application/json' : 'text/plain' 
      })
      const url = URL.createObjectURL(blob)
      const a = document.createElement('a')
      a.href = url
      a.download = `agent-history-${new Date().toISOString().split('T')[0]}.${format}`
      document.body.appendChild(a)
      a.click()
      document.body.removeChild(a)
      URL.revokeObjectURL(url)
    } catch (error) {
      console.error('Export failed:', error)
    }
  }

  // Handle clear history
  const handleClearHistory = async () => {
    const confirmed = await confirm({
      title: "Clear Agent History",
      description: "Are you sure you want to clear all agent history? This action cannot be undone.",
      confirmText: "Clear History",
      cancelText: "Cancel",
      variant: "destructive"
    })

    if (confirmed) {
      await clearHistory()
    }
  }

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'success':
        return <CheckCircle className="h-4 w-4 text-green-600" />
      case 'error':
        return <XCircle className="h-4 w-4 text-red-600" />
      case 'cancelled':
        return <AlertTriangle className="h-4 w-4 text-yellow-600" />
      case 'in_progress':
        return <Loader2 className="h-4 w-4 text-blue-600 animate-spin" />
      default:
        return <Clock className="h-4 w-4 text-gray-600" />
    }
  }

  if (error) {
    return (
      <div className="flex items-center justify-center h-full">
        <div className="text-center space-y-4">
          <XCircle className="h-12 w-12 text-red-500 mx-auto" />
          <div>
            <h3 className="text-lg font-medium">Failed to load history</h3>
            <p className="text-muted-foreground">{error}</p>
          </div>
          <Button onClick={refreshHistory}>
            <RefreshCw className="h-4 w-4 mr-2" />
            Retry
          </Button>
        </div>
      </div>
    )
  }

  return (
    <div className="h-full flex flex-col">
      {/* Header */}
      <div className="flex items-center justify-between p-4 border-b border-border">
        <div className="flex items-center gap-2">
          <Clock className="h-5 w-5 text-muted-foreground" />
          <h2 className="text-lg font-semibold">Agent History</h2>
          {stats && (
            <Badge variant="outline" className="text-xs">
              {stats.totalTasks} tasks
            </Badge>
          )}
        </div>
        
        <div className="flex items-center gap-2">
          <Button
            variant="outline"
            size="sm"
            onClick={() => setShowStats(!showStats)}
          >
            <BarChart3 className="h-4 w-4 mr-2" />
            Stats
          </Button>
          
          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <Button variant="outline" size="sm">
                <Download className="h-4 w-4 mr-2" />
                Export
              </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent>
              <DropdownMenuLabel>Export Format</DropdownMenuLabel>
              <DropdownMenuSeparator />
              <DropdownMenuItem onClick={() => handleExport('json')}>
                JSON
              </DropdownMenuItem>
              <DropdownMenuItem onClick={() => handleExport('csv')}>
                CSV
              </DropdownMenuItem>
              <DropdownMenuItem onClick={() => handleExport('markdown')}>
                Markdown
              </DropdownMenuItem>
            </DropdownMenuContent>
          </DropdownMenu>
          
          <Button variant="outline" size="sm" onClick={refreshHistory}>
            <RefreshCw className="h-4 w-4" />
          </Button>
          
          <Button variant="outline" size="sm" onClick={handleClearHistory}>
            <Trash2 className="h-4 w-4" />
          </Button>
        </div>
      </div>

      {/* Stats Panel */}
      {showStats && stats && (
        <div className="p-4 border-b border-border bg-accent/30">
          <div className="grid grid-cols-2 md:grid-cols-4 lg:grid-cols-6 gap-4">
            <div className="text-center">
              <div className="text-2xl font-bold text-green-600">{stats.successfulTasks}</div>
              <div className="text-xs text-muted-foreground">Successful</div>
            </div>
            <div className="text-center">
              <div className="text-2xl font-bold text-red-600">{stats.failedTasks}</div>
              <div className="text-xs text-muted-foreground">Failed</div>
            </div>
            <div className="text-center">
              <div className="text-2xl font-bold">{stats.totalTokensUsed.toLocaleString()}</div>
              <div className="text-xs text-muted-foreground">Tokens</div>
            </div>
            <div className="text-center">
              <div className="text-2xl font-bold">${stats.totalCost.toFixed(2)}</div>
              <div className="text-xs text-muted-foreground">Cost</div>
            </div>
            <div className="text-center">
              <div className="text-2xl font-bold">{(stats.averageDuration / 1000).toFixed(1)}s</div>
              <div className="text-xs text-muted-foreground">Avg Duration</div>
            </div>
            <div className="text-center">
              <div className="text-2xl font-bold">{((stats.successfulTasks / stats.totalTasks) * 100).toFixed(0)}%</div>
              <div className="text-xs text-muted-foreground">Success Rate</div>
            </div>
          </div>
        </div>
      )}

      {/* Filters */}
      <div className="p-4 border-b border-border space-y-3">
        <div className="flex items-center gap-2">
          <div className="relative flex-1">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
            <Input
              placeholder="Search tasks..."
              value={searchQuery}
              onChange={(e) => handleSearch(e.target.value)}
              className="pl-9"
            />
          </div>
          
          <Select value={selectedAgentType} onValueChange={handleAgentTypeChange}>
            <SelectTrigger className="w-48">
              <SelectValue placeholder="All Agents" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="all">All Agents</SelectItem>
              {Object.entries(AGENT_TYPE_NAMES).map(([type, name]) => (
                <SelectItem key={type} value={type}>
                  {name}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
          
          <Select value={selectedStatus} onValueChange={handleStatusChange}>
            <SelectTrigger className="w-40">
              <SelectValue placeholder="All Status" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="all">All Status</SelectItem>
              {Object.entries(STATUS_CONFIG).map(([status, config]) => (
                <SelectItem key={status} value={status}>
                  <div className="flex items-center gap-2">
                    {getStatusIcon(status)}
                    {config.label}
                  </div>
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
          
          {(filter.searchQuery || filter.agentTypes || filter.status) && (
            <Button variant="outline" size="sm" onClick={clearFilter}>
              Clear Filters
            </Button>
          )}
        </div>
      </div>

      {/* Content */}
      <div className="flex-1 overflow-hidden">
        {isLoading ? (
          <div className="flex items-center justify-center h-full">
            <div className="text-center space-y-4">
              <Loader2 className="h-8 w-8 animate-spin mx-auto text-muted-foreground" />
              <p className="text-muted-foreground">Loading agent history...</p>
            </div>
          </div>
        ) : entries.length === 0 ? (
          <div className="flex items-center justify-center h-full">
            <div className="text-center space-y-4">
              <Clock className="h-12 w-12 text-muted-foreground mx-auto" />
              <div>
                <h3 className="text-lg font-medium">No history yet</h3>
                <p className="text-muted-foreground">
                  Agent task history will appear here once agents start executing tasks.
                </p>
              </div>
            </div>
          </div>
        ) : (
          <ScrollArea className="h-full">
            <div className="p-4 space-y-3">
              {entries.map((entry) => (
                <HistoryCard key={entry.id} entry={entry} />
              ))}
            </div>
          </ScrollArea>
        )}
      </div>

      {/* Confirmation Dialog */}
      <ConfirmationDialog {...confirmationProps} />
    </div>
  )
}
