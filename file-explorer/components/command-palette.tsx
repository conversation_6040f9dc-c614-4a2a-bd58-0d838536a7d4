"use client"

import { useState, useEffect } from "react"
import { Command } from "lucide-react"
import { Button } from "@/components/ui/button"
import { ScrollArea } from "@/components/ui/scroll-area"
import { Input } from "@/components/ui/input"
import { useDialog } from "@/components/dialogs"

type CommandItem = {
  id: string
  name: string
  description: string
  shortcut?: string[]
  category: string
  action?: () => void
}

// Command items data
const COMMAND_ITEMS: CommandItem[] = [
  {
    id: "new-file",
    name: "New File",
    description: "Create a new file",
    shortcut: ["Ctrl", "N"],
    category: "File",
    action: () => console.log("New file"),
  },
  {
    id: "open-file",
    name: "Open File",
    description: "Open a file",
    shortcut: ["Ctrl", "O"],
    category: "File",
    action: () => console.log("Open file"),
  },
  {
    id: "save-file",
    name: "Save File",
    description: "Save current file",
    shortcut: ["Ctrl", "S"],
    category: "File",
    action: () => console.log("Save file"),
  },
  {
    id: "format-document",
    name: "Format Document",
    description: "Format the current document",
    shortcut: ["Shift", "Alt", "F"],
    category: "Edit",
    action: () => console.log("Format document"),
  },
  {
    id: "find",
    name: "Find",
    description: "Find in current file",
    shortcut: ["Ctrl", "F"],
    category: "Edit",
    action: () => console.log("Find"),
  },
  {
    id: "replace",
    name: "Replace",
    description: "Find and replace in current file",
    shortcut: ["Ctrl", "H"],
    category: "Edit",
    action: () => console.log("Replace"),
  },
  {
    id: "toggle-sidebar",
    name: "Toggle Sidebar",
    description: "Show or hide the sidebar",
    shortcut: ["Ctrl", "B"],
    category: "View",
    action: () => console.log("Toggle sidebar"),
  },
  {
    id: "toggle-terminal",
    name: "Toggle Terminal",
    description: "Show or hide the terminal",
    shortcut: ["Ctrl", "`"],
    category: "View",
    action: () => console.log("Toggle terminal"),
  },
  {
    id: "toggle-ai-assistant",
    name: "Toggle AI Assistant",
    description: "Show or hide the AI assistant",
    category: "AI",
    action: () => console.log("Toggle AI assistant"),
  },
  {
    id: "generate-code",
    name: "Generate Code",
    description: "Generate code with AI",
    category: "AI",
    action: () => console.log("Generate code"),
  },
  {
    id: "explain-code",
    name: "Explain Code",
    description: "Explain selected code with AI",
    category: "AI",
    action: () => console.log("Explain code"),
  },
  {
    id: "optimize-code",
    name: "Optimize Code",
    description: "Optimize selected code with AI",
    category: "AI",
    action: () => console.log("Optimize code"),
  },
]

export default function CommandPalette() {
  const { openDialog, closeDialog } = useDialog()

  const openCommandPalette = () => {
    openDialog('command-palette', <CommandPaletteDialog onClose={() => closeDialog('command-palette')} />, {
      size: 'md',
      closable: true,
      position: 'center'
    })
  }

  useEffect(() => {
    const handleKeyDown = (e: KeyboardEvent) => {
      if ((e.ctrlKey || e.metaKey) && e.key === "p") {
        e.preventDefault()
        openCommandPalette()
      }
    }

    window.addEventListener("keydown", handleKeyDown)
    return () => window.removeEventListener("keydown", handleKeyDown)
  }, [])

  return (
    <Button
      variant="ghost"
      size="icon"
      className="h-8 w-8 text-muted-foreground hover:text-foreground"
      onClick={openCommandPalette}
    >
      <Command className="h-4 w-4" />
    </Button>
  )
}

function CommandPaletteDialog({ onClose }: { onClose: () => void }) {
  const [search, setSearch] = useState("")
  const [filteredItems, setFilteredItems] = useState<CommandItem[]>(COMMAND_ITEMS)

  useEffect(() => {
    if (search) {
      setFilteredItems(
        COMMAND_ITEMS.filter(
          (item) =>
            item.name.toLowerCase().includes(search.toLowerCase()) ||
            item.description.toLowerCase().includes(search.toLowerCase()) ||
            item.category.toLowerCase().includes(search.toLowerCase()),
        ),
      )
    } else {
      setFilteredItems(COMMAND_ITEMS)
    }
  }, [search])

  const executeCommand = (item: CommandItem) => {
    if (item.action) {
      item.action()
    }
    onClose()
  }

  return (
    <div className="p-0 gap-0 max-w-xl bg-background border-border">
      <div className="p-4 border-b border-editor-border">
        <Input
          className="w-full bg-background border-0 focus-visible:ring-0 text-foreground placeholder-muted-foreground text-lg"
          placeholder="Search commands..."
          value={search}
          onChange={(e) => setSearch(e.target.value)}
          autoFocus
        />
      </div>
      <ScrollArea className="max-h-[60vh]">
        <div className="p-2">
          {filteredItems.length > 0 ? (
            Object.entries(
              filteredItems.reduce<Record<string, CommandItem[]>>((acc, item) => {
                if (!acc[item.category]) {
                  acc[item.category] = []
                }
                acc[item.category].push(item)
                return acc
              }, {}),
            ).map(([category, items]) => (
              <div key={category} className="mb-4">
                <div className="px-2 py-1 text-xs font-medium text-muted-foreground uppercase tracking-wider">
                  {category}
                </div>
                <div className="mt-1">
                  {items.map((item) => (
                    <div
                      key={item.id}
                      className="px-2 py-1.5 rounded-md hover:bg-accent hover:text-accent-foreground cursor-pointer"
                      onClick={() => executeCommand(item)}
                    >
                      <div className="flex items-center justify-between">
                        <div>
                          <div className="text-sm font-medium">{item.name}</div>
                          <div className="text-xs text-muted-foreground">{item.description}</div>
                        </div>
                        {item.shortcut && (
                          <div className="flex items-center space-x-1">
                            {item.shortcut.map((key, index) => (
                              <span
                                key={index}
                                className="px-1.5 py-0.5 rounded bg-muted text-xs text-muted-foreground border border-border"
                              >
                                {key}
                              </span>
                            ))}
                          </div>
                        )}
                      </div>
                    </div>
                  ))}
                </div>
              </div>
            ))
          ) : (
            <div className="p-4 text-center text-muted-foreground">No commands found</div>
          )}
        </div>
      </ScrollArea>
    </div>
  )
}


