"use client"

import { useState, useEffect } from "react"
import { Button } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Textarea } from "@/components/ui/textarea"
import { useBoard } from "./board-context"
import { useDialog } from "@/components/dialogs"

interface BoardSettingsDialogProps {
  boardId: string
}

export function BoardSettingsDialog({
  boardId,
}: BoardSettingsDialogProps) {
  const { openDialog, closeDialog } = useDialog()

  const openBoardSettingsDialog = () => {
    openDialog('board-settings', <BoardSettingsDialogContent
      boardId={boardId}
      onClose={() => closeDialog('board-settings')}
    />, {
      size: 'md',
      closable: true,
      position: 'center'
    })
  }

  return (
    <Button onClick={openBoardSettingsDialog}>
      Board Settings
    </Button>
  )
}

interface BoardSettingsDialogContentProps {
  boardId: string
  onClose: () => void
}

function BoardSettingsDialogContent({
  boardId,
  onClose,
}: BoardSettingsDialogContentProps) {
  const { allBoardsMetadata, activeBoard, updateBoard } = useBoard()

  // Find board metadata or use activeBoard if it matches
  const boardMetadata = allBoardsMetadata.find(b => b.id === boardId)
  const boardToEdit = activeBoard && activeBoard.id === boardId ? activeBoard : boardMetadata

  const [name, setName] = useState(boardToEdit?.name || "")
  const [description, setDescription] = useState(boardToEdit?.description || "")

  useEffect(() => {
    if (open) {
      // Use activeBoard if it matches the boardId, otherwise use metadata
      const currentBoardToEdit = activeBoard && activeBoard.id === boardId ? activeBoard : boardMetadata
      if (currentBoardToEdit) {
        setName(currentBoardToEdit.name);
        setDescription(currentBoardToEdit.description || "");
      } else {
        setName("");
        setDescription("");
      }
    }
  }, [open, boardId, allBoardsMetadata, activeBoard, boardMetadata]);

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault()
    if (boardToEdit && boardToEdit.id) {
        updateBoard(boardToEdit.id, name, description)
    }
    onClose()
  }

  return (
    <div className="sm:max-w-[500px] bg-background rounded-lg shadow-lg p-6">
      <div className="mb-6">
        <h2 className="text-lg font-semibold">Board Settings{boardToEdit ? `: ${boardToEdit.name}` : ''}</h2>
      </div>
        {boardToEdit && boardToEdit.id ? (
          <form onSubmit={handleSubmit}>
            <div className="grid gap-4 py-4">
              <div className="grid grid-cols-4 items-center gap-4">
                <label htmlFor="name" className="text-right text-sm">
                  Board Name
                </label>
                <Input
                  id="name"
                  value={name}
                  onChange={(e) => setName(e.target.value)}
                  className="col-span-3"
                  required
                />
              </div>
              <div className="grid grid-cols-4 items-center gap-4">
                <label htmlFor="description" className="text-right text-sm">
                  Description
                </label>
                <Textarea
                  id="description"
                  value={description}
                  onChange={(e) => setDescription(e.target.value)}
                  className="col-span-3"
                  rows={3}
                />
              </div>
            </div>
            <div className="flex justify-end gap-2 mt-6">
              <Button type="button" variant="outline" onClick={onClose}>
                Cancel
              </Button>
              <Button type="submit">Save Changes</Button>
            </div>
          </form>
        ) : (
          <div className="py-4 text-center text-muted-foreground">
            Board not found or an error occurred.
          </div>
        )}
    </div>
  )
}