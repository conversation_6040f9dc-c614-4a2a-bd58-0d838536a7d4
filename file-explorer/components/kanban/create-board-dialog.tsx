"use client"

import { useState } from "react"
import { Button } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Textarea } from "@/components/ui/textarea"
import { useDialog } from "@/components/dialogs"

interface CreateBoardDialogProps {
  onCreateBoard: (name: string, description?: string) => void
}

export function CreateBoardDialog({
  onCreateBoard,
}: CreateBoardDialogProps) {
  const { openDialog, closeDialog } = useDialog()

  const openCreateBoardDialog = () => {
    openDialog('create-board', <CreateBoardDialogContent
      onCreateBoard={onCreateBoard}
      onClose={() => closeDialog('create-board')}
    />, {
      size: 'md',
      closable: true,
      position: 'center'
    })
  }

  return (
    <Button onClick={openCreateBoardDialog}>
      Create Board
    </Button>
  )
}

interface CreateBoardDialogContentProps {
  onCreateBoard: (name: string, description?: string) => void
  onClose: () => void
}

function CreateBoardDialogContent({
  onCreateBoard,
  onClose,
}: CreateBoardDialogContentProps) {
  const [name, setName] = useState("")
  const [description, setDescription] = useState("")

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault()
    onCreateBoard(name, description)
    setName("")
    setDescription("")
    onClose()
  }

  return (
    <div className="sm:max-w-[500px] bg-background rounded-lg shadow-lg p-6">
      <div className="mb-6">
        <h2 className="text-lg font-semibold">Create New Board</h2>
      </div>
      <form onSubmit={handleSubmit}>
        <div className="grid gap-4 py-4">
          <div className="grid grid-cols-4 items-center gap-4">
            <label htmlFor="name" className="text-right text-sm">
              Board Name
            </label>
            <Input
              id="name"
              value={name}
              onChange={(e) => setName(e.target.value)}
              className="col-span-3"
              required
            />
          </div>
          <div className="grid grid-cols-4 items-center gap-4">
            <label htmlFor="description" className="text-right text-sm">
              Description
            </label>
            <Textarea
              id="description"
              value={description}
              onChange={(e) => setDescription(e.target.value)}
              className="col-span-3"
              placeholder="Optional board description..."
            />
          </div>
        </div>
        <div className="flex justify-end gap-2 mt-6">
          <Button type="button" variant="outline" onClick={onClose}>
            Cancel
          </Button>
          <Button type="submit">Create Board</Button>
        </div>
      </form>
    </div>
  )
}
