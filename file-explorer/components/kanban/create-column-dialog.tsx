"use client"

import { useState } from "react"
import { Button } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { useDialog } from "@/components/dialogs"

interface CreateColumnDialogProps {
  onCreateColumn: (title: string) => void
}

export function CreateColumnDialog({
  onCreateColumn,
}: CreateColumnDialogProps) {
  const { openDialog, closeDialog } = useDialog()

  const openCreateColumnDialog = () => {
    openDialog('create-column', <CreateColumnDialogContent
      onCreateColumn={onCreateColumn}
      onClose={() => closeDialog('create-column')}
    />, {
      size: 'sm',
      closable: true,
      position: 'center'
    })
  }

  return (
    <Button onClick={openCreateColumnDialog}>
      Create Column
    </Button>
  )
}

interface CreateColumnDialogContentProps {
  onCreateColumn: (title: string) => void
  onClose: () => void
}

function CreateColumnDialogContent({
  onCreateColumn,
  onClose,
}: CreateColumnDialogContentProps) {
  const [title, setTitle] = useState("")

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault()
    onCreateColumn(title)
    setTitle("")
    onClose()
  }

  return (
    <div className="sm:max-w-[425px] bg-background rounded-lg shadow-lg p-6">
      <div className="mb-6">
        <h2 className="text-lg font-semibold">Create New Column</h2>
      </div>
      <form onSubmit={handleSubmit}>
        <div className="grid gap-4 py-4">
          <div className="grid grid-cols-4 items-center gap-4">
            <label htmlFor="title" className="text-right text-sm">
              Title
            </label>
            <Input
              id="title"
              value={title}
              onChange={(e) => setTitle(e.target.value)}
              className="col-span-3"
              required
            />
          </div>
        </div>
        <div className="flex justify-end gap-2 mt-6">
          <Button type="button" variant="outline" onClick={onClose}>
            Cancel
          </Button>
          <Button type="submit">Create Column</Button>
        </div>
      </form>
    </div>
  )
}
