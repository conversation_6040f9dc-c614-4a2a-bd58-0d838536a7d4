"use client"

import { useState } from "react"
import { Button } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { useDialog } from "@/components/dialogs"

interface CreateSwimlaneDialogProps {
  onCreateSwimlane: (title: string) => void
}

export function CreateSwimlaneDialog({
  onCreateSwimlane,
}: CreateSwimlaneDialogProps) {
  const { openDialog, closeDialog } = useDialog()

  const openCreateSwimlaneDialog = () => {
    openDialog('create-swimlane', <CreateSwimlaneDialogContent
      onCreateSwimlane={onCreateSwimlane}
      onClose={() => closeDialog('create-swimlane')}
    />, {
      size: 'sm',
      closable: true,
      position: 'center'
    })
  }

  return (
    <Button onClick={openCreateSwimlaneDialog}>
      Create Swimlane
    </Button>
  )
}

interface CreateSwimlaneDialogContentProps {
  onCreateSwimlane: (title: string) => void
  onClose: () => void
}

function CreateSwimlaneDialogContent({
  onCreateSwimlane,
  onClose,
}: CreateSwimlaneDialogContentProps) {
  const [title, setTitle] = useState("")

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault()
    onCreateSwimlane(title)
    setTitle("")
    onClose()
  }

  return (
    <div className="sm:max-w-[425px] bg-background rounded-lg shadow-lg p-6">
      <div className="mb-6">
        <h2 className="text-lg font-semibold">Create New Swimlane</h2>
      </div>
      <form onSubmit={handleSubmit}>
        <div className="grid gap-4 py-4">
          <div className="grid grid-cols-4 items-center gap-4">
            <label htmlFor="title" className="text-right text-sm">
              Title
            </label>
            <Input
              id="title"
              value={title}
              onChange={(e) => setTitle(e.target.value)}
              className="col-span-3"
              required
            />
          </div>
        </div>
        <div className="flex justify-end gap-2 mt-6">
          <Button type="button" variant="outline" onClick={onClose}>
            Cancel
          </Button>
          <Button type="submit">Create Swimlane</Button>
        </div>
      </form>
    </div>
  )
}
