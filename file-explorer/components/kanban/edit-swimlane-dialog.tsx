"use client"

import { useState, useEffect } from "react"
import { But<PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Swimlane } from "./board-context"
import { useDialog } from "@/components/dialogs"

interface EditSwimlaneDialogProps {
  onUpdateSwimlane: (id: string, title: string) => void
  swimlane: Swimlane | null
}

export function EditSwimlaneDialog({
  onUpdateSwimlane,
  swimlane,
}: EditSwimlaneDialogProps) {
  const { openDialog, closeDialog } = useDialog()

  const openEditSwimlaneDialog = () => {
    if (!swimlane) return

    openDialog('edit-swimlane', <EditSwimlaneDialogContent
      onUpdateSwimlane={onUpdateSwimlane}
      swimlane={swimlane}
      onClose={() => closeDialog('edit-swimlane')}
    />, {
      size: 'sm',
      closable: true,
      position: 'center'
    })
  }

  return (
    <Button onClick={openEditSwimlaneDialog} disabled={!swimlane}>
      Edit Swimlane
    </Button>
  )
}

interface EditSwimlaneDialogContentProps {
  onUpdateSwimlane: (id: string, title: string) => void
  swimlane: Swimlane
  onClose: () => void
}

function EditSwimlaneDialogContent({
  onUpdateSwimlane,
  swimlane,
  onClose,
}: EditSwimlaneDialogContentProps) {
  const [title, setTitle] = useState("")

  useEffect(() => {
    if (swimlane) {
      setTitle(swimlane.title)
    }
  }, [swimlane])

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault()
    onUpdateSwimlane(swimlane.id, title)
    onClose()
  }

  return (
    <div className="sm:max-w-[425px] bg-background rounded-lg shadow-lg p-6">
      <div className="mb-6">
        <h2 className="text-lg font-semibold">Edit Swimlane</h2>
      </div>
      <form onSubmit={handleSubmit}>
        <div className="grid gap-4 py-4">
          <div className="grid grid-cols-4 items-center gap-4">
            <label htmlFor="title" className="text-right text-sm">
              Title
            </label>
            <Input
              id="title"
              value={title}
              onChange={(e) => setTitle(e.target.value)}
              className="col-span-3"
              required
            />
          </div>
        </div>
        <div className="flex justify-end gap-2 mt-6">
          <Button type="button" variant="outline" onClick={onClose}>
            Cancel
          </Button>
          <Button type="submit">Update Swimlane</Button>
        </div>
      </form>
    </div>
  )
}