/**
 * File Sidebar State Management
 * React Context for managing file sidebar state and actions
 */

import React, { createContext, useContext, useReducer, ReactNode, useMemo, useEffect } from 'react'
import { FileSystemItem } from './types'

export interface FileSidebarState {
  // Core state
  projects: FileSystemItem[]
  searchQuery: string
  selectedFile: FileSystemItem | null
  recentProjects: Array<{name: string, path: string, lastOpened: number}>

  // Dialog states
  showUnsavedChangesDialog: boolean
  showPRDDialog: boolean
  showExplorerSettings: boolean
  showOrchestrationDialog: boolean
  pendingProjectSwitch: {name: string, path: string} | null

  // Project creation state
  projectFolderPath: string | null
  currentProjectPath: string | null
  prdValidated: boolean

  // Explorer settings
  explorerSettings: {
    showHiddenFiles: boolean
    autoExpandFolders: boolean
    showFileExtensions: boolean
    compactView: boolean
    showFileIcons: boolean
    sortBy: 'name' | 'modified' | 'size' | 'type'
    sortOrder: 'asc' | 'desc'
  }
}

interface FileSidebarActions {
  // Actions
  setProjects: (projects: FileSystemItem[]) => void
  setSearchQuery: (query: string) => void
  setSelectedFile: (file: FileSystemItem | null) => void
  setRecentProjects: (projects: Array<{name: string, path: string, lastOpened: number}>) => void

  // Dialog actions
  setShowUnsavedChangesDialog: (show: boolean) => void
  setShowPRDDialog: (show: boolean) => void
  setShowExplorerSettings: (show: boolean) => void
  setShowOrchestrationDialog: (show: boolean) => void
  setPendingProjectSwitch: (project: {name: string, path: string} | null) => void

  // Project creation actions
  setProjectFolderPath: (path: string | null) => void
  setCurrentProjectPath: (path: string | null) => void
  setPrdValidated: (validated: boolean) => void

  // Explorer settings actions
  setExplorerSettings: (settings: Partial<FileSidebarState['explorerSettings']>) => void

  // Complex actions
  updateFolderExpansion: (id: number | string, expanded: boolean, files?: FileSystemItem[]) => void
  addProject: (project: FileSystemItem) => void
  removeProject: (projectId: number | string) => void
  resetProjectCreation: () => void
}

// Initial state
const initialState: Omit<FileSidebarState, keyof FileSidebarActions> = {
  projects: [],
  searchQuery: '',
  selectedFile: null,
  recentProjects: [],

  showUnsavedChangesDialog: false,
  showPRDDialog: false,
  showExplorerSettings: false,
  showOrchestrationDialog: false,
  pendingProjectSwitch: null,

  projectFolderPath: null,
  currentProjectPath: null,
  prdValidated: false,

  explorerSettings: {
    showHiddenFiles: false,
    autoExpandFolders: true,
    showFileExtensions: true,
    compactView: false,
    showFileIcons: true,
    sortBy: 'name',
    sortOrder: 'asc'
  }
}

// Action types
type FileSidebarAction =
  | { type: 'SET_PROJECTS'; payload: FileSystemItem[] }
  | { type: 'SET_SEARCH_QUERY'; payload: string }
  | { type: 'SET_SELECTED_FILE'; payload: FileSystemItem | null }
  | { type: 'SET_RECENT_PROJECTS'; payload: Array<{name: string, path: string, lastOpened: number}> }
  | { type: 'SET_SHOW_UNSAVED_CHANGES_DIALOG'; payload: boolean }
  | { type: 'SET_SHOW_PRD_DIALOG'; payload: boolean }
  | { type: 'SET_SHOW_EXPLORER_SETTINGS'; payload: boolean }
  | { type: 'SET_SHOW_ORCHESTRATION_DIALOG'; payload: boolean }
  | { type: 'SET_PENDING_PROJECT_SWITCH'; payload: {name: string, path: string} | null }
  | { type: 'SET_PROJECT_FOLDER_PATH'; payload: string | null }
  | { type: 'SET_CURRENT_PROJECT_PATH'; payload: string | null }
  | { type: 'SET_PRD_VALIDATED'; payload: boolean }
  | { type: 'SET_EXPLORER_SETTINGS'; payload: Partial<FileSidebarState['explorerSettings']> }
  | { type: 'UPDATE_FOLDER_EXPANSION'; payload: { id: number | string; expanded: boolean; files?: FileSystemItem[] } }
  | { type: 'ADD_PROJECT'; payload: FileSystemItem }
  | { type: 'REMOVE_PROJECT'; payload: number | string }
  | { type: 'RESET_PROJECT_CREATION' }

// Reducer
function fileSidebarReducer(state: typeof initialState, action: FileSidebarAction): typeof initialState {
  switch (action.type) {
    case 'SET_PROJECTS':
      return { ...state, projects: action.payload }
    case 'SET_SEARCH_QUERY':
      return { ...state, searchQuery: action.payload }
    case 'SET_SELECTED_FILE':
      return { ...state, selectedFile: action.payload }
    case 'SET_RECENT_PROJECTS':
      return { ...state, recentProjects: action.payload }
    case 'SET_SHOW_UNSAVED_CHANGES_DIALOG':
      return { ...state, showUnsavedChangesDialog: action.payload }
    case 'SET_SHOW_PRD_DIALOG':
      return { ...state, showPRDDialog: action.payload }
    case 'SET_SHOW_EXPLORER_SETTINGS':
      return { ...state, showExplorerSettings: action.payload }
    case 'SET_SHOW_ORCHESTRATION_DIALOG':
      return { ...state, showOrchestrationDialog: action.payload }
    case 'SET_PENDING_PROJECT_SWITCH':
      return { ...state, pendingProjectSwitch: action.payload }
    case 'SET_PROJECT_FOLDER_PATH':
      return { ...state, projectFolderPath: action.payload }
    case 'SET_CURRENT_PROJECT_PATH':
      return { ...state, currentProjectPath: action.payload }
    case 'SET_PRD_VALIDATED':
      return { ...state, prdValidated: action.payload }
    case 'SET_EXPLORER_SETTINGS':
      return { ...state, explorerSettings: { ...state.explorerSettings, ...action.payload } }
    case 'UPDATE_FOLDER_EXPANSION':
      const updateFolderState = (items: FileSystemItem[]): FileSystemItem[] => {
        return items.map(item => {
          if (item.id === action.payload.id) {
            return { ...item, expanded: action.payload.expanded, files: action.payload.files || item.files }
          }
          if (item.files && Array.isArray(item.files)) {
            return { ...item, files: updateFolderState(item.files) }
          }
          return item
        })
      }
      return { ...state, projects: updateFolderState(state.projects) }
    case 'ADD_PROJECT':
      const existingProject = state.projects.find(p => p.path === action.payload.path)
      if (existingProject) {
        return state
      }
      return { ...state, projects: [...state.projects, action.payload] }
    case 'REMOVE_PROJECT':
      return { ...state, projects: state.projects.filter(p => p.id !== action.payload) }
    case 'RESET_PROJECT_CREATION':
      return {
        ...state,
        projectFolderPath: null,
        currentProjectPath: null,
        prdValidated: false,
        showPRDDialog: false
      }
    default:
      return state
  }
}

// Context
const FileSidebarContext = createContext<(FileSidebarState & FileSidebarActions) | null>(null)

// Provider component
export const FileSidebarProvider: React.FC<{ children: ReactNode }> = ({ children }) => {
  const [state, dispatch] = useReducer(fileSidebarReducer, initialState)

  // ✅ CRITICAL FIX: IPC synchronization for floating windows
  useEffect(() => {
    if (typeof window !== 'undefined' && window.electronAPI?.ipc) {
      // Listen for state updates from other windows
      const handleStateSync = (syncedState: Partial<typeof initialState>) => {
        console.log('📡 Received file explorer state sync:', syncedState);

        // Update local state with synced data
        if (syncedState.projects) {
          dispatch({ type: 'SET_PROJECTS', payload: syncedState.projects });
        }
        if (syncedState.recentProjects) {
          dispatch({ type: 'SET_RECENT_PROJECTS', payload: syncedState.recentProjects });
        }
        if (syncedState.selectedFile !== undefined) {
          dispatch({ type: 'SET_SELECTED_FILE', payload: syncedState.selectedFile });
        }
        if (syncedState.currentProjectPath !== undefined) {
          dispatch({ type: 'SET_CURRENT_PROJECT_PATH', payload: syncedState.currentProjectPath });
        }
      };

      // Register IPC listener for state synchronization
      window.electronAPI.ipc.on('file-explorer-state-sync', handleStateSync);

      // Request initial state sync when component mounts
      window.electronAPI.ipc.invoke('get-file-explorer-state').then((initialState: any) => {
        if (initialState) {
          handleStateSync(initialState);
        }
      }).catch((error: any) => {
        console.warn('Failed to get initial file explorer state:', error);
      });

      // Cleanup listener on unmount
      return () => {
        if (window.electronAPI?.ipc) {
          window.electronAPI.ipc.removeListener('file-explorer-state-sync', handleStateSync);
        }
      };
    }
  }, []);

  // ✅ CRITICAL FIX: Memoize actions with IPC broadcasting
  const actions: FileSidebarActions = useMemo(() => ({
    setProjects: (projects) => {
      dispatch({ type: 'SET_PROJECTS', payload: projects });
      // Broadcast to other windows
      if (typeof window !== 'undefined' && window.electronAPI?.ipc) {
        window.electronAPI.ipc.invoke('sync-file-explorer-state', { projects });
      }
    },
    setSearchQuery: (query) => dispatch({ type: 'SET_SEARCH_QUERY', payload: query }),
    setSelectedFile: (file) => {
      dispatch({ type: 'SET_SELECTED_FILE', payload: file });
      // Broadcast to other windows
      if (typeof window !== 'undefined' && window.electronAPI?.ipc) {
        window.electronAPI.ipc.invoke('sync-file-explorer-state', { selectedFile: file });
      }
    },
    setRecentProjects: (projects) => {
      dispatch({ type: 'SET_RECENT_PROJECTS', payload: projects });
      // Broadcast to other windows
      if (typeof window !== 'undefined' && window.electronAPI?.ipc) {
        window.electronAPI.ipc.invoke('sync-file-explorer-state', { recentProjects: projects });
      }
    },
    setShowUnsavedChangesDialog: (show) => dispatch({ type: 'SET_SHOW_UNSAVED_CHANGES_DIALOG', payload: show }),
    setShowPRDDialog: (show) => dispatch({ type: 'SET_SHOW_PRD_DIALOG', payload: show }),
    setShowExplorerSettings: (show) => dispatch({ type: 'SET_SHOW_EXPLORER_SETTINGS', payload: show }),
    setShowOrchestrationDialog: (show) => dispatch({ type: 'SET_SHOW_ORCHESTRATION_DIALOG', payload: show }),
    setPendingProjectSwitch: (project) => dispatch({ type: 'SET_PENDING_PROJECT_SWITCH', payload: project }),
    setProjectFolderPath: (path) => dispatch({ type: 'SET_PROJECT_FOLDER_PATH', payload: path }),
    setCurrentProjectPath: (path) => {
      dispatch({ type: 'SET_CURRENT_PROJECT_PATH', payload: path });
      // Broadcast to other windows
      if (typeof window !== 'undefined' && window.electronAPI?.ipc) {
        window.electronAPI.ipc.invoke('sync-file-explorer-state', { currentProjectPath: path });
      }
    },
    setPrdValidated: (validated) => dispatch({ type: 'SET_PRD_VALIDATED', payload: validated }),
    setExplorerSettings: (settings) => dispatch({ type: 'SET_EXPLORER_SETTINGS', payload: settings }),
    updateFolderExpansion: (id, expanded, files) => {
      dispatch({ type: 'UPDATE_FOLDER_EXPANSION', payload: { id, expanded, files } });
      // Note: Folder expansion state is typically local to each window for UX reasons
    },
    addProject: (project) => {
      dispatch({ type: 'ADD_PROJECT', payload: project });
      // Broadcast to other windows
      if (typeof window !== 'undefined' && window.electronAPI?.ipc) {
        window.electronAPI.ipc.invoke('sync-file-explorer-state', { projects: [...state.projects, project] });
      }
    },
    removeProject: (projectId) => {
      const newProjects = state.projects.filter(p => p.id !== projectId);
      dispatch({ type: 'REMOVE_PROJECT', payload: projectId });
      // Broadcast to other windows
      if (typeof window !== 'undefined' && window.electronAPI?.ipc) {
        window.electronAPI.ipc.invoke('sync-file-explorer-state', { projects: newProjects });
      }
    },
    resetProjectCreation: () => dispatch({ type: 'RESET_PROJECT_CREATION' })
  }), [dispatch, state.projects]) // Include state.projects for addProject/removeProject

  // ✅ CRITICAL FIX: Memoize context value to prevent unnecessary re-renders
  const contextValue = useMemo(() => ({ ...state, ...actions }), [state, actions])

  return (
    <FileSidebarContext.Provider value={contextValue}>
      {children}
    </FileSidebarContext.Provider>
  )
}

// Hook to use the context
export const useFileSidebarStore = () => {
  const context = useContext(FileSidebarContext)
  if (!context) {
    throw new Error('useFileSidebarStore must be used within a FileSidebarProvider')
  }
  return context
}

// Utility function to load settings from localStorage
export const loadExplorerSettings = () => {
  try {
    const savedSettings = localStorage.getItem('explorerSettings')
    if (savedSettings) {
      const parsed = JSON.parse(savedSettings)
      // Note: This would need to be called within a component that has access to the context
      console.warn('loadExplorerSettings should be called within a component with context access')
    }
  } catch (error) {
    console.warn('Failed to parse saved explorer settings:', error)
  }
}

// Utility function to save settings to localStorage
export const saveExplorerSettings = (settings: FileSidebarState['explorerSettings']) => {
  localStorage.setItem('explorerSettings', JSON.stringify(settings))
}
