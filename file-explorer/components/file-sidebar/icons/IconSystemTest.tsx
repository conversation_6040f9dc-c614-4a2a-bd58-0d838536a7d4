/**
 * Icon System Test Component
 * For testing and demonstrating the enhanced icon system
 * This component will be removed after testing is complete
 */

import React, { useState } from 'react'
import { FileTypeIcon, IconCacheStats, useFileTypeInfo } from './FileTypeIcons'
import { getSupportedExtensions, getExtensionsByCategory } from './IconMapping'
import { getThemeNames } from './IconThemes'

export const IconSystemTest: React.FC = () => {
  const [selectedTheme, setSelectedTheme] = useState<string>('auto')
  const [selectedSize, setSelectedSize] = useState<'xs' | 'sm' | 'md' | 'lg' | 'xl'>('md')
  const [selectedVariant, setSelectedVariant] = useState<'default' | 'outline' | 'filled' | 'minimal'>('minimal')

  const supportedExtensions = getSupportedExtensions()
  const themeNames = getThemeNames()
  
  // Group extensions by category for better organization
  const languageExtensions = getExtensionsByCategory('language')
  const frameworkExtensions = getExtensionsByCategory('framework')
  const dataExtensions = getExtensionsByCategory('data')
  const mediaExtensions = getExtensionsByCategory('media')
  const documentExtensions = getExtensionsByCategory('document')
  const configExtensions = getExtensionsByCategory('config')

  const ExtensionGroup: React.FC<{ title: string; extensions: string[] }> = ({ title, extensions }) => (
    <div className="mb-6">
      <h3 className="text-sm font-semibold mb-3 text-gray-700 dark:text-gray-300">{title}</h3>
      <div className="grid grid-cols-6 gap-3">
        {extensions.map((ext) => (
          <div key={ext} className="flex flex-col items-center p-2 border rounded hover:bg-gray-50 dark:hover:bg-gray-800">
            <FileTypeIcon
              extension={ext}
              size={selectedSize}
              theme={selectedTheme as any}
              variant={selectedVariant}
              showTooltip={true}
            />
            <span className="text-xs mt-1 text-gray-600 dark:text-gray-400">{ext}</span>
          </div>
        ))}
      </div>
    </div>
  )

  return (
    <div className="p-6 max-w-6xl mx-auto">
      <div className="mb-6">
        <h1 className="text-2xl font-bold mb-4">File Type Icon System Test</h1>
        
        {/* Controls */}
        <div className="flex gap-4 mb-6 p-4 bg-gray-100 dark:bg-gray-800 rounded">
          <div>
            <label className="block text-sm font-medium mb-1">Theme:</label>
            <select 
              value={selectedTheme} 
              onChange={(e) => setSelectedTheme(e.target.value)}
              className="border rounded px-2 py-1 text-sm"
            >
              <option value="auto">Auto</option>
              {themeNames.map(theme => (
                <option key={theme} value={theme}>{theme}</option>
              ))}
            </select>
          </div>
          
          <div>
            <label className="block text-sm font-medium mb-1">Size:</label>
            <select 
              value={selectedSize} 
              onChange={(e) => setSelectedSize(e.target.value as any)}
              className="border rounded px-2 py-1 text-sm"
            >
              <option value="xs">XS</option>
              <option value="sm">SM</option>
              <option value="md">MD</option>
              <option value="lg">LG</option>
              <option value="xl">XL</option>
            </select>
          </div>
          
          <div>
            <label className="block text-sm font-medium mb-1">Variant:</label>
            <select 
              value={selectedVariant} 
              onChange={(e) => setSelectedVariant(e.target.value as any)}
              className="border rounded px-2 py-1 text-sm"
            >
              <option value="default">Default</option>
              <option value="outline">Outline</option>
              <option value="filled">Filled</option>
              <option value="minimal">Minimal</option>
            </select>
          </div>
        </div>

        {/* Statistics */}
        <div className="mb-6 p-4 bg-blue-50 dark:bg-blue-900/20 rounded">
          <h2 className="text-lg font-semibold mb-2">System Statistics</h2>
          <p className="text-sm text-gray-600 dark:text-gray-400">
            Total supported file types: <strong>{supportedExtensions.length}</strong>
          </p>
          <div className="mt-2 text-xs text-gray-500">
            Languages: {languageExtensions.length} | 
            Frameworks: {frameworkExtensions.length} | 
            Data: {dataExtensions.length} | 
            Media: {mediaExtensions.length} | 
            Documents: {documentExtensions.length} | 
            Config: {configExtensions.length}
          </div>
        </div>
      </div>

      {/* Icon Groups */}
      <div className="space-y-6">
        <ExtensionGroup title="Programming Languages" extensions={languageExtensions} />
        <ExtensionGroup title="Web Technologies" extensions={frameworkExtensions} />
        <ExtensionGroup title="Data & Configuration" extensions={[...dataExtensions, ...configExtensions]} />
        <ExtensionGroup title="Documents" extensions={documentExtensions} />
        <ExtensionGroup title="Media Files" extensions={mediaExtensions} />
      </div>

      {/* Performance Stats */}
      <div className="mt-8">
        <IconCacheStats />
      </div>

      {/* Test Individual File Info */}
      <div className="mt-8 p-4 bg-gray-50 dark:bg-gray-800 rounded">
        <h3 className="text-lg font-semibold mb-3">File Type Information Test</h3>
        <div className="grid grid-cols-3 gap-4">
          {['js', 'ts', 'py', 'html', 'json', 'md'].map(ext => {
            const info = useFileTypeInfo(ext)
            return (
              <div key={ext} className="p-3 border rounded">
                <div className="flex items-center mb-2">
                  <FileTypeIcon extension={ext} size="sm" className="mr-2" />
                  <span className="font-medium">{ext}</span>
                </div>
                <div className="text-xs text-gray-600 dark:text-gray-400">
                  <div>Category: {info.category}</div>
                  <div>Display: {info.displayText}</div>
                  <div>Priority: {info.priority}</div>
                </div>
              </div>
            )
          })}
        </div>
      </div>
    </div>
  )
}

export default IconSystemTest
