/**
 * Icon Cache System
 * Provides performance optimization for icon rendering through caching and preloading
 */

import React from 'react'
import { IconDefinition } from './IconMapping'

export interface CacheEntry {
  component: React.ComponentType<any>
  timestamp: number
  accessCount: number
  lastAccessed: number
}

export interface CacheStats {
  totalEntries: number
  hitCount: number
  missCount: number
  hitRate: number
  memoryUsage: number
}

export interface IconCacheConfig {
  maxCacheSize: number
  maxAge: number // in milliseconds
  preloadCommonIcons: boolean
  enableStats: boolean
  cleanupInterval: number // in milliseconds
}

/**
 * Default cache configuration
 */
const DEFAULT_CONFIG: IconCacheConfig = {
  maxCacheSize: 100,
  maxAge: 30 * 60 * 1000, // 30 minutes
  preloadCommonIcons: true,
  enableStats: true,
  cleanupInterval: 5 * 60 * 1000 // 5 minutes
}

/**
 * Most commonly used file extensions for preloading
 */
const COMMON_EXTENSIONS = [
  'js', 'ts', 'tsx', 'jsx', 'py', 'java', 'html', 'css', 'scss',
  'json', 'md', 'txt', 'png', 'jpg', 'svg', 'mp4', 'mp3'
]

/**
 * Icon Cache Manager
 * Handles caching, preloading, and cleanup of icon components
 */
export class IconCache {
  private cache = new Map<string, CacheEntry>()
  private stats = {
    hitCount: 0,
    missCount: 0,
    totalRequests: 0
  }
  private config: IconCacheConfig
  private cleanupTimer?: NodeJS.Timeout
  private preloadPromise?: Promise<void>

  constructor(config: Partial<IconCacheConfig> = {}) {
    this.config = { ...DEFAULT_CONFIG, ...config }
    
    if (this.config.preloadCommonIcons) {
      this.preloadCommonIcons()
    }
    
    this.startCleanupTimer()
  }

  /**
   * Generate cache key for an icon
   */
  private generateCacheKey(
    extension: string,
    theme: string,
    size: string,
    variant: string
  ): string {
    return `${extension}-${theme}-${size}-${variant}`
  }

  /**
   * Get icon from cache or create new one
   */
  public getIcon(
    extension: string,
    iconDefinition: IconDefinition,
    theme: string = 'light',
    size: string = 'md',
    variant: string = 'default'
  ): React.ComponentType<any> {
    const cacheKey = this.generateCacheKey(extension, theme, size, variant)
    
    // Check cache first
    const cached = this.cache.get(cacheKey)
    if (cached && this.isValidCacheEntry(cached)) {
      this.updateCacheStats(cached, true)
      return cached.component
    }

    // Cache miss - create new icon component
    this.stats.missCount++
    this.stats.totalRequests++
    
    const iconComponent = this.createIconComponent(iconDefinition, theme, size, variant)
    this.setCacheEntry(cacheKey, iconComponent)
    
    return iconComponent
  }

  /**
   * Create optimized icon component
   */
  private createIconComponent(
    iconDefinition: IconDefinition,
    theme: string,
    size: string,
    variant: string
  ): React.ComponentType<any> {
    const IconComponent = React.memo<{ className?: string }>(({ className }) => {
      const IconElement = iconDefinition.icon
      
      return React.createElement(IconElement, {
        className: `file-icon file-icon-${size} file-icon-${variant} ${className || ''}`,
        'data-file-type': iconDefinition.category,
        'aria-label': `${iconDefinition.displayText || 'File'} icon`
      })
    })
    
    IconComponent.displayName = `FileIcon-${iconDefinition.displayText || 'Unknown'}`
    
    return IconComponent
  }

  /**
   * Check if cache entry is still valid
   */
  private isValidCacheEntry(entry: CacheEntry): boolean {
    const now = Date.now()
    return (now - entry.timestamp) < this.config.maxAge
  }

  /**
   * Update cache statistics
   */
  private updateCacheStats(entry: CacheEntry, isHit: boolean): void {
    if (isHit) {
      this.stats.hitCount++
      entry.accessCount++
      entry.lastAccessed = Date.now()
    }
    this.stats.totalRequests++
  }

  /**
   * Set cache entry with size management
   */
  private setCacheEntry(key: string, component: React.ComponentType<any>): void {
    // Remove oldest entries if cache is full
    if (this.cache.size >= this.config.maxCacheSize) {
      this.evictOldestEntries()
    }

    const entry: CacheEntry = {
      component,
      timestamp: Date.now(),
      accessCount: 1,
      lastAccessed: Date.now()
    }

    this.cache.set(key, entry)
  }

  /**
   * Evict oldest cache entries
   */
  private evictOldestEntries(): void {
    const entries = Array.from(this.cache.entries())
    
    // Sort by last accessed time (oldest first)
    entries.sort(([, a], [, b]) => a.lastAccessed - b.lastAccessed)
    
    // Remove oldest 25% of entries
    const removeCount = Math.floor(this.config.maxCacheSize * 0.25)
    for (let i = 0; i < removeCount && entries.length > 0; i++) {
      const [key] = entries[i]
      this.cache.delete(key)
    }
  }

  /**
   * Preload commonly used icons
   */
  public async preloadCommonIcons(): Promise<void> {
    if (this.preloadPromise) {
      return this.preloadPromise
    }

    this.preloadPromise = this.performPreload()
    return this.preloadPromise
  }

  /**
   * Perform the actual preloading
   */
  private async performPreload(): Promise<void> {
    try {
      const { getIconDefinition } = await import('./IconMapping')
      
      // Preload common extensions with default settings
      for (const extension of COMMON_EXTENSIONS) {
        const iconDefinition = getIconDefinition(extension)
        this.getIcon(extension, iconDefinition, 'light', 'md', 'default')
        this.getIcon(extension, iconDefinition, 'dark', 'md', 'default')
      }
      
      console.log(`✅ Preloaded ${COMMON_EXTENSIONS.length} common file type icons`)
    } catch (error) {
      console.warn('Failed to preload common icons:', error)
    }
  }

  /**
   * Get cache statistics
   */
  public getStats(): CacheStats {
    const hitRate = this.stats.totalRequests > 0 
      ? (this.stats.hitCount / this.stats.totalRequests) * 100 
      : 0

    return {
      totalEntries: this.cache.size,
      hitCount: this.stats.hitCount,
      missCount: this.stats.missCount,
      hitRate: Math.round(hitRate * 100) / 100,
      memoryUsage: this.estimateMemoryUsage()
    }
  }

  /**
   * Estimate memory usage of cache
   */
  private estimateMemoryUsage(): number {
    // Rough estimate: each cache entry ~1KB
    return this.cache.size * 1024
  }

  /**
   * Clear cache
   */
  public clear(): void {
    this.cache.clear()
    this.stats = {
      hitCount: 0,
      missCount: 0,
      totalRequests: 0
    }
  }

  /**
   * Start cleanup timer
   */
  private startCleanupTimer(): void {
    if (this.cleanupTimer) {
      clearInterval(this.cleanupTimer)
    }

    this.cleanupTimer = setInterval(() => {
      this.cleanup()
    }, this.config.cleanupInterval)
  }

  /**
   * Cleanup expired cache entries
   */
  private cleanup(): void {
    const now = Date.now()
    const expiredKeys: string[] = []

    for (const [key, entry] of this.cache.entries()) {
      if ((now - entry.timestamp) > this.config.maxAge) {
        expiredKeys.push(key)
      }
    }

    for (const key of expiredKeys) {
      this.cache.delete(key)
    }

    if (expiredKeys.length > 0 && this.config.enableStats) {
      console.log(`🧹 Cleaned up ${expiredKeys.length} expired icon cache entries`)
    }
  }

  /**
   * Destroy cache and cleanup resources
   */
  public destroy(): void {
    if (this.cleanupTimer) {
      clearInterval(this.cleanupTimer)
      this.cleanupTimer = undefined
    }
    
    this.clear()
    this.preloadPromise = undefined
  }
}

/**
 * Global icon cache instance
 */
let globalIconCache: IconCache | null = null

/**
 * Get or create global icon cache
 */
export function getIconCache(config?: Partial<IconCacheConfig>): IconCache {
  if (!globalIconCache) {
    globalIconCache = new IconCache(config)
  }
  return globalIconCache
}

/**
 * Destroy global icon cache
 */
export function destroyIconCache(): void {
  if (globalIconCache) {
    globalIconCache.destroy()
    globalIconCache = null
  }
}

/**
 * React hook for using icon cache
 */
export function useIconCache(config?: Partial<IconCacheConfig>): IconCache {
  const [cache] = React.useState(() => getIconCache(config))
  
  React.useEffect(() => {
    return () => {
      // Don't destroy global cache on component unmount
      // It should persist across component lifecycles
    }
  }, [])
  
  return cache
}
