/**
 * Icon Themes Configuration
 * Provides theme-aware color schemes for file type icons
 */

export interface IconThemeColors {
  // Language colors
  javascript: string
  typescript: string
  python: string
  java: string
  csharp: string
  go: string
  rust: string
  php: string
  ruby: string
  swift: string
  kotlin: string
  dart: string
  
  // Web technology colors
  html: string
  css: string
  scss: string
  vue: string
  svelte: string
  angular: string
  
  // Data format colors
  json: string
  yaml: string
  xml: string
  toml: string
  
  // Document colors
  markdown: string
  text: string
  
  // Media colors
  image: string
  video: string
  audio: string
  
  // Default colors
  default: string
  folder: string
  unknown: string
}

export interface IconTheme {
  name: string
  displayName: string
  colors: IconThemeColors
  backgrounds: Partial<IconThemeColors>
  isDark: boolean
}

/**
 * Light theme configuration
 */
export const LIGHT_THEME: IconTheme = {
  name: 'light',
  displayName: 'Light Theme',
  isDark: false,
  colors: {
    // Language colors - vibrant but readable on light backgrounds
    javascript: '#F7DF1E',
    typescript: '#3178C6',
    python: '#3776AB',
    java: '#ED8B00',
    csharp: '#239120',
    go: '#00ADD8',
    rust: '#CE422B',
    php: '#777BB4',
    ruby: '#CC342D',
    swift: '#FA7343',
    kotlin: '#7F52FF',
    dart: '#0175C2',
    
    // Web technology colors
    html: '#E34F26',
    css: '#1572B6',
    scss: '#CF649A',
    vue: '#4FC08D',
    svelte: '#FF3E00',
    angular: '#DD0031',
    
    // Data format colors
    json: '#FFA500',
    yaml: '#CB171E',
    xml: '#FF6600',
    toml: '#9C4221',
    
    // Document colors
    markdown: '#083FA1',
    text: '#6B7280',
    
    // Media colors
    image: '#FF6B6B',
    video: '#6C5CE7',
    audio: '#A29BFE',
    
    // Default colors
    default: '#6B7280',
    folder: '#3B82F6',
    unknown: '#9CA3AF'
  },
  backgrounds: {
    // Subtle background colors with low opacity
    javascript: '#F7DF1E15',
    typescript: '#3178C615',
    python: '#3776AB15',
    java: '#ED8B0015',
    csharp: '#23912015',
    go: '#00ADD815',
    rust: '#CE422B15',
    php: '#777BB415',
    ruby: '#CC342D15',
    swift: '#FA734315',
    kotlin: '#7F52FF15',
    dart: '#0175C215',
    
    html: '#E34F2615',
    css: '#1572B615',
    scss: '#CF649A15',
    vue: '#4FC08D15',
    svelte: '#FF3E0015',
    angular: '#DD003115',
    
    json: '#FFA50015',
    yaml: '#CB171E15',
    xml: '#FF660015',
    toml: '#9C422115',
    
    markdown: '#083FA115',
    text: '#6B728015',
    
    image: '#FF6B6B15',
    video: '#6C5CE715',
    audio: '#A29BFE15',
    
    default: '#6B728015',
    folder: '#3B82F615',
    unknown: '#9CA3AF15'
  }
}

/**
 * Dark theme configuration
 */
export const DARK_THEME: IconTheme = {
  name: 'dark',
  displayName: 'Dark Theme',
  isDark: true,
  colors: {
    // Language colors - adjusted for dark backgrounds
    javascript: '#F7DF1E',
    typescript: '#4FC3F7',
    python: '#4FC3F7',
    java: '#FFB74D',
    csharp: '#81C784',
    go: '#4DD0E1',
    rust: '#FF8A65',
    php: '#B39DDB',
    ruby: '#F48FB1',
    swift: '#FFB74D',
    kotlin: '#B39DDB',
    dart: '#4FC3F7',
    
    // Web technology colors
    html: '#FF8A65',
    css: '#4FC3F7',
    scss: '#F48FB1',
    vue: '#81C784',
    svelte: '#FF8A65',
    angular: '#F48FB1',
    
    // Data format colors
    json: '#FFD54F',
    yaml: '#F48FB1',
    xml: '#FFB74D',
    toml: '#BCAAA4',
    
    // Document colors
    markdown: '#4FC3F7',
    text: '#B0BEC5',
    
    // Media colors
    image: '#F48FB1',
    video: '#B39DDB',
    audio: '#CE93D8',
    
    // Default colors
    default: '#B0BEC5',
    folder: '#4FC3F7',
    unknown: '#90A4AE'
  },
  backgrounds: {
    // Darker background colors for dark theme
    javascript: '#F7DF1E20',
    typescript: '#4FC3F720',
    python: '#4FC3F720',
    java: '#FFB74D20',
    csharp: '#81C78420',
    go: '#4DD0E120',
    rust: '#FF8A6520',
    php: '#B39DDB20',
    ruby: '#F48FB120',
    swift: '#FFB74D20',
    kotlin: '#B39DDB20',
    dart: '#4FC3F720',
    
    html: '#FF8A6520',
    css: '#4FC3F720',
    scss: '#F48FB120',
    vue: '#81C78420',
    svelte: '#FF8A6520',
    angular: '#F48FB120',
    
    json: '#FFD54F20',
    yaml: '#F48FB120',
    xml: '#FFB74D20',
    toml: '#BCAAA420',
    
    markdown: '#4FC3F720',
    text: '#B0BEC520',
    
    image: '#F48FB120',
    video: '#B39DDB20',
    audio: '#CE93D820',
    
    default: '#B0BEC520',
    folder: '#4FC3F720',
    unknown: '#90A4AE20'
  }
}

/**
 * High contrast theme for accessibility
 */
export const HIGH_CONTRAST_THEME: IconTheme = {
  name: 'high-contrast',
  displayName: 'High Contrast',
  isDark: false,
  colors: {
    // High contrast colors for accessibility
    javascript: '#000000',
    typescript: '#0000FF',
    python: '#008000',
    java: '#FF8000',
    csharp: '#800080',
    go: '#008080',
    rust: '#800000',
    php: '#4B0082',
    ruby: '#DC143C',
    swift: '#FF4500',
    kotlin: '#9400D3',
    dart: '#006400',
    
    html: '#FF0000',
    css: '#0000FF',
    scss: '#FF1493',
    vue: '#228B22',
    svelte: '#FF4500',
    angular: '#DC143C',
    
    json: '#FF8C00',
    yaml: '#B22222',
    xml: '#FF6347',
    toml: '#8B4513',
    
    markdown: '#000080',
    text: '#000000',
    
    image: '#FF69B4',
    video: '#8A2BE2',
    audio: '#9932CC',
    
    default: '#000000',
    folder: '#0000FF',
    unknown: '#696969'
  },
  backgrounds: {
    // High contrast backgrounds
    javascript: '#FFFF00',
    typescript: '#E6F3FF',
    python: '#F0FFF0',
    java: '#FFF8DC',
    csharp: '#F8F8FF',
    go: '#F0FFFF',
    rust: '#FFF0F0',
    php: '#F5F0FF',
    ruby: '#FFF0F5',
    swift: '#FFF8DC',
    kotlin: '#F5F0FF',
    dart: '#F0FFF0',
    
    html: '#FFE4E1',
    css: '#E6F3FF',
    scss: '#FFF0F5',
    vue: '#F0FFF0',
    svelte: '#FFF8DC',
    angular: '#FFF0F5',
    
    json: '#FFF8DC',
    yaml: '#FFF0F0',
    xml: '#FFF8DC',
    toml: '#F5F5DC',
    
    markdown: '#F0F8FF',
    text: '#FFFFFF',
    
    image: '#FFF0F5',
    video: '#F5F0FF',
    audio: '#F5F0FF',
    
    default: '#FFFFFF',
    folder: '#E6F3FF',
    unknown: '#F5F5F5'
  }
}

/**
 * Available themes registry
 */
export const AVAILABLE_THEMES: Record<string, IconTheme> = {
  light: LIGHT_THEME,
  dark: DARK_THEME,
  'high-contrast': HIGH_CONTRAST_THEME
}

/**
 * Get theme by name with fallback to light theme
 */
export function getTheme(themeName: string): IconTheme {
  return AVAILABLE_THEMES[themeName] || LIGHT_THEME
}

/**
 * Get theme names
 */
export function getThemeNames(): string[] {
  return Object.keys(AVAILABLE_THEMES)
}

/**
 * Auto-detect theme based on system preferences
 */
export function getSystemTheme(): string {
  if (typeof window !== 'undefined') {
    // Check for system dark mode preference
    if (window.matchMedia && window.matchMedia('(prefers-color-scheme: dark)').matches) {
      return 'dark'
    }
    
    // Check for high contrast preference
    if (window.matchMedia && window.matchMedia('(prefers-contrast: high)').matches) {
      return 'high-contrast'
    }
  }
  
  return 'light'
}

/**
 * Theme-aware color getter
 */
export function getThemedColor(
  colorKey: keyof IconThemeColors,
  themeName: string = 'light',
  isBackground: boolean = false
): string {
  const theme = getTheme(themeName)
  
  if (isBackground && theme.backgrounds[colorKey]) {
    return theme.backgrounds[colorKey]!
  }
  
  return theme.colors[colorKey]
}
