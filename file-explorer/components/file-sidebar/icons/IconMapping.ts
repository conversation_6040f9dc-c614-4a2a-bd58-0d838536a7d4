/**
 * Icon Mapping Configuration
 * Comprehensive file type to icon mapping based on VS Code and modern IDE standards
 */

import { LucideIcon } from "lucide-react"

export interface IconDefinition {
  icon: LucideIcon | React.ComponentType<any>
  color: string
  backgroundColor: string
  category: 'language' | 'framework' | 'data' | 'media' | 'document' | 'config'
  priority: number
  displayText?: string
}

export interface IconTheme {
  name: string
  colors: Record<string, string>
  backgrounds: Record<string, string>
}

// Import Lucide icons for file types
import {
  FileText, FileCode, FileImage, FileVideo, FileAudio,
  Database, Settings, Archive, Lock, Key, Zap, Cpu,
  Globe, Palette, Package, Layers, GitBranch, Terminal,
  Code2, Braces, Hash, FileJson, FileSpreadsheet
} from "lucide-react"

/**
 * Comprehensive file type mappings based on VS Code icon themes
 * Organized by category for better maintainability
 */
export const FILE_TYPE_MAPPINGS: Record<string, IconDefinition> = {
  // Programming Languages - JavaScript/TypeScript Family
  'js': {
    icon: FileCode,
    color: '#F7DF1E',
    backgroundColor: '#F7DF1E15',
    category: 'language',
    priority: 1,
    displayText: 'JS'
  },
  'jsx': {
    icon: FileCode,
    color: '#61DAFB',
    backgroundColor: '#61DAFB15',
    category: 'language',
    priority: 1,
    displayText: 'JSX'
  },
  'ts': {
    icon: FileCode,
    color: '#3178C6',
    backgroundColor: '#3178C615',
    category: 'language',
    priority: 1,
    displayText: 'TS'
  },
  'tsx': {
    icon: FileCode,
    color: '#3178C6',
    backgroundColor: '#3178C615',
    category: 'language',
    priority: 1,
    displayText: 'TSX'
  },

  // Programming Languages - Popular Languages
  'py': {
    icon: FileCode,
    color: '#3776AB',
    backgroundColor: '#3776AB15',
    category: 'language',
    priority: 1,
    displayText: 'PY'
  },
  'java': {
    icon: FileCode,
    color: '#ED8B00',
    backgroundColor: '#ED8B0015',
    category: 'language',
    priority: 1,
    displayText: 'JAVA'
  },
  'c': {
    icon: FileCode,
    color: '#A8B9CC',
    backgroundColor: '#A8B9CC15',
    category: 'language',
    priority: 1,
    displayText: 'C'
  },
  'cpp': {
    icon: FileCode,
    color: '#00599C',
    backgroundColor: '#00599C15',
    category: 'language',
    priority: 1,
    displayText: 'C++'
  },
  'cs': {
    icon: FileCode,
    color: '#239120',
    backgroundColor: '#23912015',
    category: 'language',
    priority: 1,
    displayText: 'C#'
  },
  'go': {
    icon: FileCode,
    color: '#00ADD8',
    backgroundColor: '#00ADD815',
    category: 'language',
    priority: 1,
    displayText: 'GO'
  },
  'rs': {
    icon: FileCode,
    color: '#CE422B',
    backgroundColor: '#CE422B15',
    category: 'language',
    priority: 1,
    displayText: 'RS'
  },
  'php': {
    icon: FileCode,
    color: '#777BB4',
    backgroundColor: '#777BB415',
    category: 'language',
    priority: 1,
    displayText: 'PHP'
  },
  'rb': {
    icon: FileCode,
    color: '#CC342D',
    backgroundColor: '#CC342D15',
    category: 'language',
    priority: 1,
    displayText: 'RB'
  },
  'swift': {
    icon: FileCode,
    color: '#FA7343',
    backgroundColor: '#FA734315',
    category: 'language',
    priority: 1,
    displayText: 'SWIFT'
  },
  'kt': {
    icon: FileCode,
    color: '#7F52FF',
    backgroundColor: '#7F52FF15',
    category: 'language',
    priority: 1,
    displayText: 'KT'
  },
  'dart': {
    icon: FileCode,
    color: '#0175C2',
    backgroundColor: '#0175C215',
    category: 'language',
    priority: 1,
    displayText: 'DART'
  },
  'scala': {
    icon: FileCode,
    color: '#DC322F',
    backgroundColor: '#DC322F15',
    category: 'language',
    priority: 2,
    displayText: 'SCALA'
  },
  'r': {
    icon: FileCode,
    color: '#276DC3',
    backgroundColor: '#276DC315',
    category: 'language',
    priority: 2,
    displayText: 'R'
  },
  'lua': {
    icon: FileCode,
    color: '#2C2D72',
    backgroundColor: '#2C2D7215',
    category: 'language',
    priority: 2,
    displayText: 'LUA'
  },
  'perl': {
    icon: FileCode,
    color: '#39457E',
    backgroundColor: '#39457E15',
    category: 'language',
    priority: 2,
    displayText: 'PERL'
  },
  'haskell': {
    icon: FileCode,
    color: '#5D4F85',
    backgroundColor: '#5D4F8515',
    category: 'language',
    priority: 2,
    displayText: 'HS'
  },
  'clojure': {
    icon: FileCode,
    color: '#5881D8',
    backgroundColor: '#5881D815',
    category: 'language',
    priority: 2,
    displayText: 'CLJ'
  },
  'elixir': {
    icon: FileCode,
    color: '#4B275F',
    backgroundColor: '#4B275F15',
    category: 'language',
    priority: 2,
    displayText: 'EX'
  },
  'erlang': {
    icon: FileCode,
    color: '#A90533',
    backgroundColor: '#A9053315',
    category: 'language',
    priority: 2,
    displayText: 'ERL'
  },

  // Web Technologies
  'html': {
    icon: Globe,
    color: '#E34F26',
    backgroundColor: '#E34F2615',
    category: 'framework',
    priority: 1,
    displayText: 'HTML'
  },
  'css': {
    icon: Palette,
    color: '#1572B6',
    backgroundColor: '#1572B615',
    category: 'framework',
    priority: 1,
    displayText: 'CSS'
  },
  'scss': {
    icon: Palette,
    color: '#CF649A',
    backgroundColor: '#CF649A15',
    category: 'framework',
    priority: 1,
    displayText: 'SCSS'
  },
  'sass': {
    icon: Palette,
    color: '#CF649A',
    backgroundColor: '#CF649A15',
    category: 'framework',
    priority: 1,
    displayText: 'SASS'
  },
  'less': {
    icon: Palette,
    color: '#1D365D',
    backgroundColor: '#1D365D15',
    category: 'framework',
    priority: 1,
    displayText: 'LESS'
  },
  'vue': {
    icon: Layers,
    color: '#4FC08D',
    backgroundColor: '#4FC08D15',
    category: 'framework',
    priority: 1,
    displayText: 'VUE'
  },
  'svelte': {
    icon: Layers,
    color: '#FF3E00',
    backgroundColor: '#FF3E0015',
    category: 'framework',
    priority: 1,
    displayText: 'SVELTE'
  },
  'angular': {
    icon: Layers,
    color: '#DD0031',
    backgroundColor: '#DD003115',
    category: 'framework',
    priority: 1,
    displayText: 'NG'
  },

  // Data & Configuration Files
  'json': {
    icon: Braces,
    color: '#FFA500',
    backgroundColor: '#FFA50015',
    category: 'data',
    priority: 1,
    displayText: 'JSON'
  },
  'yaml': {
    icon: FileText,
    color: '#CB171E',
    backgroundColor: '#CB171E15',
    category: 'data',
    priority: 1,
    displayText: 'YAML'
  },
  'yml': {
    icon: FileText,
    color: '#CB171E',
    backgroundColor: '#CB171E15',
    category: 'data',
    priority: 1,
    displayText: 'YML'
  },
  'xml': {
    icon: Code2,
    color: '#FF6600',
    backgroundColor: '#FF660015',
    category: 'data',
    priority: 1,
    displayText: 'XML'
  },
  'toml': {
    icon: Settings,
    color: '#9C4221',
    backgroundColor: '#9C422115',
    category: 'config',
    priority: 1,
    displayText: 'TOML'
  },
  'ini': {
    icon: Settings,
    color: '#6D6D6D',
    backgroundColor: '#6D6D6D15',
    category: 'config',
    priority: 1,
    displayText: 'INI'
  },
  'env': {
    icon: Key,
    color: '#ECD53F',
    backgroundColor: '#ECD53F15',
    category: 'config',
    priority: 1,
    displayText: 'ENV'
  },
  'config': {
    icon: Settings,
    color: '#6D6D6D',
    backgroundColor: '#6D6D6D15',
    category: 'config',
    priority: 1,
    displayText: 'CFG'
  },

  // Documentation
  'md': {
    icon: FileText,
    color: '#083FA1',
    backgroundColor: '#083FA115',
    category: 'document',
    priority: 1,
    displayText: 'MD'
  },
  'mdx': {
    icon: FileText,
    color: '#1B1F24',
    backgroundColor: '#1B1F2415',
    category: 'document',
    priority: 1,
    displayText: 'MDX'
  },
  'txt': {
    icon: FileText,
    color: '#6D6D6D',
    backgroundColor: '#6D6D6D15',
    category: 'document',
    priority: 1,
    displayText: 'TXT'
  },
  'rst': {
    icon: FileText,
    color: '#141414',
    backgroundColor: '#14141415',
    category: 'document',
    priority: 2,
    displayText: 'RST'
  },
  'adoc': {
    icon: FileText,
    color: '#E40046',
    backgroundColor: '#E4004615',
    category: 'document',
    priority: 2,
    displayText: 'ADOC'
  },

  // Media Files
  'png': {
    icon: FileImage,
    color: '#FF6B6B',
    backgroundColor: '#FF6B6B15',
    category: 'media',
    priority: 1,
    displayText: 'PNG'
  },
  'jpg': {
    icon: FileImage,
    color: '#4ECDC4',
    backgroundColor: '#4ECDC415',
    category: 'media',
    priority: 1,
    displayText: 'JPG'
  },
  'jpeg': {
    icon: FileImage,
    color: '#4ECDC4',
    backgroundColor: '#4ECDC415',
    category: 'media',
    priority: 1,
    displayText: 'JPEG'
  },
  'gif': {
    icon: FileImage,
    color: '#45B7D1',
    backgroundColor: '#45B7D115',
    category: 'media',
    priority: 1,
    displayText: 'GIF'
  },
  'svg': {
    icon: FileImage,
    color: '#FFB347',
    backgroundColor: '#FFB34715',
    category: 'media',
    priority: 1,
    displayText: 'SVG'
  },
  'webp': {
    icon: FileImage,
    color: '#96CEB4',
    backgroundColor: '#96CEB415',
    category: 'media',
    priority: 1,
    displayText: 'WEBP'
  },
  'ico': {
    icon: FileImage,
    color: '#FFEAA7',
    backgroundColor: '#FFEAA715',
    category: 'media',
    priority: 1,
    displayText: 'ICO'
  },
  'mp4': {
    icon: FileVideo,
    color: '#6C5CE7',
    backgroundColor: '#6C5CE715',
    category: 'media',
    priority: 1,
    displayText: 'MP4'
  },
  'mp3': {
    icon: FileAudio,
    color: '#A29BFE',
    backgroundColor: '#A29BFE15',
    category: 'media',
    priority: 1,
    displayText: 'MP3'
  },
  'wav': {
    icon: FileAudio,
    color: '#FD79A8',
    backgroundColor: '#FD79A815',
    category: 'media',
    priority: 1,
    displayText: 'WAV'
  },

  // Additional Programming Languages
  'asm': {
    icon: FileCode,
    color: '#6E4C13',
    backgroundColor: '#6E4C1315',
    category: 'language',
    priority: 2,
    displayText: 'ASM'
  },
  'sh': {
    icon: Terminal,
    color: '#4EAA25',
    backgroundColor: '#4EAA2515',
    category: 'language',
    priority: 1,
    displayText: 'SH'
  },
  'bash': {
    icon: Terminal,
    color: '#4EAA25',
    backgroundColor: '#4EAA2515',
    category: 'language',
    priority: 1,
    displayText: 'BASH'
  },
  'zsh': {
    icon: Terminal,
    color: '#4EAA25',
    backgroundColor: '#4EAA2515',
    category: 'language',
    priority: 1,
    displayText: 'ZSH'
  },
  'fish': {
    icon: Terminal,
    color: '#4EAA25',
    backgroundColor: '#4EAA2515',
    category: 'language',
    priority: 1,
    displayText: 'FISH'
  },
  'ps1': {
    icon: Terminal,
    color: '#012456',
    backgroundColor: '#01245615',
    category: 'language',
    priority: 1,
    displayText: 'PS1'
  },
  'bat': {
    icon: Terminal,
    color: '#C1F12E',
    backgroundColor: '#C1F12E15',
    category: 'language',
    priority: 1,
    displayText: 'BAT'
  },
  'cmd': {
    icon: Terminal,
    color: '#C1F12E',
    backgroundColor: '#C1F12E15',
    category: 'language',
    priority: 1,
    displayText: 'CMD'
  },

  // Additional Web Technologies
  'astro': {
    icon: Layers,
    color: '#FF5D01',
    backgroundColor: '#FF5D0115',
    category: 'framework',
    priority: 1,
    displayText: 'ASTRO'
  },
  'remix': {
    icon: Layers,
    color: '#000000',
    backgroundColor: '#00000015',
    category: 'framework',
    priority: 1,
    displayText: 'REMIX'
  },
  'next': {
    icon: Layers,
    color: '#000000',
    backgroundColor: '#00000015',
    category: 'framework',
    priority: 1,
    displayText: 'NEXT'
  },
  'nuxt': {
    icon: Layers,
    color: '#00DC82',
    backgroundColor: '#00DC8215',
    category: 'framework',
    priority: 1,
    displayText: 'NUXT'
  },
  'gatsby': {
    icon: Layers,
    color: '#663399',
    backgroundColor: '#66339915',
    category: 'framework',
    priority: 1,
    displayText: 'GATSBY'
  },

  // Additional Data Formats
  'csv': {
    icon: FileSpreadsheet,
    color: '#207245',
    backgroundColor: '#20724515',
    category: 'data',
    priority: 1,
    displayText: 'CSV'
  },
  'tsv': {
    icon: FileSpreadsheet,
    color: '#207245',
    backgroundColor: '#20724515',
    category: 'data',
    priority: 1,
    displayText: 'TSV'
  },
  'sql': {
    icon: Database,
    color: '#336791',
    backgroundColor: '#33679115',
    category: 'data',
    priority: 1,
    displayText: 'SQL'
  },
  'graphql': {
    icon: Code2,
    color: '#E10098',
    backgroundColor: '#E1009815',
    category: 'data',
    priority: 1,
    displayText: 'GQL'
  },
  'proto': {
    icon: Code2,
    color: '#4285F4',
    backgroundColor: '#4285F415',
    category: 'data',
    priority: 2,
    displayText: 'PROTO'
  },
  'avro': {
    icon: Database,
    color: '#1F4E79',
    backgroundColor: '#1F4E7915',
    category: 'data',
    priority: 2,
    displayText: 'AVRO'
  },
  'parquet': {
    icon: Database,
    color: '#50C878',
    backgroundColor: '#50C87815',
    category: 'data',
    priority: 2,
    displayText: 'PARQUET'
  },

  // Additional Configuration Files
  'properties': {
    icon: Settings,
    color: '#6D6D6D',
    backgroundColor: '#6D6D6D15',
    category: 'config',
    priority: 1,
    displayText: 'PROPS'
  },
  'conf': {
    icon: Settings,
    color: '#6D6D6D',
    backgroundColor: '#6D6D6D15',
    category: 'config',
    priority: 1,
    displayText: 'CONF'
  },
  'cfg': {
    icon: Settings,
    color: '#6D6D6D',
    backgroundColor: '#6D6D6D15',
    category: 'config',
    priority: 1,
    displayText: 'CFG'
  },
  'lock': {
    icon: Lock,
    color: '#FF6B35',
    backgroundColor: '#FF6B3515',
    category: 'config',
    priority: 1,
    displayText: 'LOCK'
  },
  'gitignore': {
    icon: GitBranch,
    color: '#F05032',
    backgroundColor: '#F0503215',
    category: 'config',
    priority: 1,
    displayText: 'GIT'
  },
  'dockerignore': {
    icon: Package,
    color: '#2496ED',
    backgroundColor: '#2496ED15',
    category: 'config',
    priority: 1,
    displayText: 'DOCKER'
  },
  'editorconfig': {
    icon: Settings,
    color: '#FEFEFE',
    backgroundColor: '#FEFEFE15',
    category: 'config',
    priority: 1,
    displayText: 'EDITOR'
  },

  // Additional Media Types
  'bmp': {
    icon: FileImage,
    color: '#FF6B6B',
    backgroundColor: '#FF6B6B15',
    category: 'media',
    priority: 2,
    displayText: 'BMP'
  },
  'tiff': {
    icon: FileImage,
    color: '#4ECDC4',
    backgroundColor: '#4ECDC415',
    category: 'media',
    priority: 2,
    displayText: 'TIFF'
  },
  'webm': {
    icon: FileVideo,
    color: '#6C5CE7',
    backgroundColor: '#6C5CE715',
    category: 'media',
    priority: 1,
    displayText: 'WEBM'
  },
  'avi': {
    icon: FileVideo,
    color: '#6C5CE7',
    backgroundColor: '#6C5CE715',
    category: 'media',
    priority: 1,
    displayText: 'AVI'
  },
  'ogg': {
    icon: FileAudio,
    color: '#A29BFE',
    backgroundColor: '#A29BFE15',
    category: 'media',
    priority: 1,
    displayText: 'OGG'
  },

  // Archive Files
  'zip': {
    icon: Archive,
    color: '#FFD700',
    backgroundColor: '#FFD70015',
    category: 'media',
    priority: 1,
    displayText: 'ZIP'
  },
  'rar': {
    icon: Archive,
    color: '#FFD700',
    backgroundColor: '#FFD70015',
    category: 'media',
    priority: 1,
    displayText: 'RAR'
  },
  '7z': {
    icon: Archive,
    color: '#FFD700',
    backgroundColor: '#FFD70015',
    category: 'media',
    priority: 1,
    displayText: '7Z'
  },
  'tar': {
    icon: Archive,
    color: '#FFD700',
    backgroundColor: '#FFD70015',
    category: 'media',
    priority: 1,
    displayText: 'TAR'
  },
  'gz': {
    icon: Archive,
    color: '#FFD700',
    backgroundColor: '#FFD70015',
    category: 'media',
    priority: 1,
    displayText: 'GZ'
  }
}

/**
 * Icon themes for different visual preferences
 */
export const ICON_THEMES: Record<string, IconTheme> = {
  light: {
    name: 'Light Theme',
    colors: {
      default: '#6B7280',
      primary: '#3B82F6',
      secondary: '#6B7280',
      success: '#10B981',
      warning: '#F59E0B',
      error: '#EF4444'
    },
    backgrounds: {
      default: '#F3F4F615',
      primary: '#3B82F615',
      secondary: '#6B728015',
      success: '#10B98115',
      warning: '#F59E0B15',
      error: '#EF444415'
    }
  },
  dark: {
    name: 'Dark Theme',
    colors: {
      default: '#9CA3AF',
      primary: '#60A5FA',
      secondary: '#9CA3AF',
      success: '#34D399',
      warning: '#FBBF24',
      error: '#F87171'
    },
    backgrounds: {
      default: '#374151',
      primary: '#1E40AF',
      secondary: '#4B5563',
      success: '#065F46',
      warning: '#92400E',
      error: '#991B1B'
    }
  },
  'high-contrast': {
    name: 'High Contrast',
    colors: {
      default: '#000000',
      primary: '#0000FF',
      secondary: '#000000',
      success: '#008000',
      warning: '#FF8000',
      error: '#FF0000'
    },
    backgrounds: {
      default: '#FFFFFF',
      primary: '#E6F3FF',
      secondary: '#F5F5F5',
      success: '#E6FFE6',
      warning: '#FFF2E6',
      error: '#FFE6E6'
    }
  }
}

/**
 * Get icon definition for a file extension
 */
export function getIconDefinition(extension: string): IconDefinition {
  const normalizedExt = extension.toLowerCase().replace('.', '')
  return FILE_TYPE_MAPPINGS[normalizedExt] || {
    icon: FileText,
    color: '#6B7280',
    backgroundColor: '#6B728015',
    category: 'document',
    priority: 10,
    displayText: 'DOC'
  }
}

/**
 * Get all supported file extensions
 */
export function getSupportedExtensions(): string[] {
  return Object.keys(FILE_TYPE_MAPPINGS)
}

/**
 * Get file extensions by category
 */
export function getExtensionsByCategory(category: IconDefinition['category']): string[] {
  return Object.entries(FILE_TYPE_MAPPINGS)
    .filter(([, def]) => def.category === category)
    .map(([ext]) => ext)
}
