/**
 * File Type Icons Component
 * Advanced icon system with comprehensive file type support, theming, and performance optimization
 */

import React, { useMemo } from 'react'
import { cn } from '@/lib/utils'
import { getIconDefinition } from './IconMapping'
import { getTheme, getSystemTheme } from './IconThemes'
import { useIconCache } from './IconCache'

export interface FileTypeIconProps {
  extension: string
  size?: 'xs' | 'sm' | 'md' | 'lg' | 'xl'
  theme?: 'light' | 'dark' | 'high-contrast' | 'auto'
  variant?: 'default' | 'outline' | 'filled' | 'minimal'
  className?: string
  showTooltip?: boolean
  showLabel?: boolean
  onClick?: () => void
}

/**
 * Size mappings for different icon sizes
 */
const SIZE_CLASSES = {
  xs: 'w-3 h-3 text-xs',
  sm: 'w-4 h-4 text-xs',
  md: 'w-5 h-5 text-xs',
  lg: 'w-6 h-6 text-sm',
  xl: 'w-8 h-8 text-base'
} as const

/**
 * Variant style mappings
 */
const VARIANT_CLASSES = {
  default: 'rounded-sm',
  outline: 'rounded-sm border border-current',
  filled: 'rounded-sm',
  minimal: 'rounded-none'
} as const

/**
 * Main FileTypeIcons component with advanced features
 */
export const FileTypeIcons: React.FC<FileTypeIconProps> = ({
  extension,
  size = 'md',
  theme = 'auto',
  variant = 'default',
  className,
  showTooltip = false,
  showLabel = false,
  onClick
}) => {
  const iconCache = useIconCache()
  
  // Resolve theme
  const resolvedTheme = useMemo(() => {
    if (theme === 'auto') {
      return getSystemTheme()
    }
    return theme
  }, [theme])

  // Get icon definition and theme
  const iconDefinition = useMemo(() => getIconDefinition(extension), [extension])
  const themeConfig = useMemo(() => getTheme(resolvedTheme), [resolvedTheme])

  // Get cached icon component
  const IconComponent = useMemo(() => {
    return iconCache.getIcon(extension, iconDefinition, resolvedTheme, size, variant)
  }, [iconCache, extension, iconDefinition, resolvedTheme, size, variant])

  // Generate styles based on theme and variant
  const iconStyles = useMemo(() => {
    const baseStyles = {
      color: iconDefinition.color,
      backgroundColor: variant === 'filled' ? iconDefinition.backgroundColor : 'transparent'
    }

    // Apply theme overrides if needed
    if (themeConfig.isDark && variant !== 'minimal') {
      // Adjust colors for dark theme
      return {
        ...baseStyles,
        filter: 'brightness(1.1) saturate(0.9)'
      }
    }

    return baseStyles
  }, [iconDefinition, themeConfig, variant])

  // Generate CSS classes
  const iconClasses = useMemo(() => {
    return cn(
      'flex items-center justify-center flex-shrink-0',
      SIZE_CLASSES[size],
      VARIANT_CLASSES[variant],
      variant === 'filled' && 'text-white',
      variant === 'outline' && 'bg-transparent',
      onClick && 'cursor-pointer hover:opacity-80 transition-opacity',
      className
    )
  }, [size, variant, onClick, className])

  // Tooltip content
  const tooltipContent = useMemo(() => {
    if (!showTooltip) return undefined
    
    return `${iconDefinition.displayText || extension.toUpperCase()} file (${iconDefinition.category})`
  }, [showTooltip, iconDefinition, extension])

  // Label content
  const labelContent = useMemo(() => {
    if (!showLabel) return null
    
    return (
      <span className="ml-1 text-xs text-muted-foreground truncate">
        {iconDefinition.displayText || extension.toUpperCase()}
      </span>
    )
  }, [showLabel, iconDefinition, extension])

  const iconElement = (
    <div
      className={iconClasses}
      style={iconStyles}
      onClick={onClick}
      title={tooltipContent}
      role={onClick ? 'button' : 'img'}
      tabIndex={onClick ? 0 : undefined}
      aria-label={`${iconDefinition.displayText || extension.toUpperCase()} file icon`}
      aria-describedby={showTooltip ? `tooltip-${extension}` : undefined}
      onKeyDown={onClick ? (e) => {
        if (e.key === 'Enter' || e.key === ' ') {
          e.preventDefault()
          onClick()
        }
      } : undefined}
    >
      {variant === 'minimal' ? (
        // Minimal variant shows just the text
        <span className="font-bold">
          {iconDefinition.displayText || 'DOC'}
        </span>
      ) : (
        // Other variants show the icon
        <IconComponent className="w-full h-full" />
      )}
    </div>
  )

  // Wrap with label if needed
  if (showLabel) {
    return (
      <div className="flex items-center">
        {iconElement}
        {labelContent}
      </div>
    )
  }

  return iconElement
}

/**
 * Optimized file icon component with React.memo
 */
export const FileTypeIcon = React.memo(FileTypeIcons)

/**
 * Hook for getting file type information
 */
export function useFileTypeInfo(extension: string) {
  return useMemo(() => {
    const iconDefinition = getIconDefinition(extension)
    return {
      extension,
      category: iconDefinition.category,
      displayText: iconDefinition.displayText,
      color: iconDefinition.color,
      priority: iconDefinition.priority
    }
  }, [extension])
}

/**
 * Bulk icon preloader component
 */
export const IconPreloader: React.FC<{ extensions: string[] }> = ({ extensions }) => {
  const iconCache = useIconCache()
  
  React.useEffect(() => {
    // Preload icons for the given extensions
    const preloadIcons = async () => {
      for (const ext of extensions) {
        const iconDefinition = getIconDefinition(ext)
        iconCache.getIcon(ext, iconDefinition, 'light', 'md', 'default')
        iconCache.getIcon(ext, iconDefinition, 'dark', 'md', 'default')
      }
    }
    
    preloadIcons()
  }, [extensions, iconCache])
  
  return null // This component doesn't render anything
}

/**
 * Icon statistics component for debugging
 */
export const IconCacheStats: React.FC = () => {
  const iconCache = useIconCache()
  const [stats, setStats] = React.useState(iconCache.getStats())
  
  React.useEffect(() => {
    const interval = setInterval(() => {
      setStats(iconCache.getStats())
    }, 1000)
    
    return () => clearInterval(interval)
  }, [iconCache])
  
  if (process.env.NODE_ENV !== 'development') {
    return null
  }
  
  return (
    <div className="fixed bottom-4 right-4 bg-black/80 text-white p-2 rounded text-xs font-mono">
      <div>Icon Cache Stats:</div>
      <div>Entries: {stats.totalEntries}</div>
      <div>Hit Rate: {stats.hitRate}%</div>
      <div>Memory: {Math.round(stats.memoryUsage / 1024)}KB</div>
    </div>
  )
}

/**
 * File extension utilities
 */
export function getFileExtension(filename: string): string {
  const lastDot = filename.lastIndexOf('.')
  if (lastDot === -1 || lastDot === filename.length - 1) {
    return ''
  }
  return filename.substring(lastDot + 1).toLowerCase()
}

export function isImageFile(extension: string): boolean {
  const imageExtensions = ['png', 'jpg', 'jpeg', 'gif', 'svg', 'webp', 'ico', 'bmp', 'tiff']
  return imageExtensions.includes(extension.toLowerCase())
}

export function isVideoFile(extension: string): boolean {
  const videoExtensions = ['mp4', 'avi', 'mov', 'wmv', 'flv', 'webm', 'mkv']
  return videoExtensions.includes(extension.toLowerCase())
}

export function isAudioFile(extension: string): boolean {
  const audioExtensions = ['mp3', 'wav', 'ogg', 'flac', 'aac', 'm4a']
  return audioExtensions.includes(extension.toLowerCase())
}

export function isCodeFile(extension: string): boolean {
  const iconDefinition = getIconDefinition(extension)
  return iconDefinition.category === 'language' || iconDefinition.category === 'framework'
}

export function isDataFile(extension: string): boolean {
  const iconDefinition = getIconDefinition(extension)
  return iconDefinition.category === 'data' || iconDefinition.category === 'config'
}

/**
 * Default export for backward compatibility
 */
export default FileTypeIcon
