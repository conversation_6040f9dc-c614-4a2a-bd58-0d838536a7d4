/**
 * Performance Test for Icon System
 * Tests rendering performance and memory usage of the enhanced icon system
 */

import { getIconCache } from './IconCache'
import { getSupportedExtensions, getIconDefinition } from './IconMapping'

export interface PerformanceTestResult {
  totalExtensions: number
  renderTime: number
  cacheHitRate: number
  memoryUsage: number
  averageRenderTime: number
  success: boolean
  errors: string[]
}

/**
 * Run comprehensive performance test
 */
export async function runIconPerformanceTest(): Promise<PerformanceTestResult> {
  const errors: string[] = []
  const startTime = performance.now()
  
  try {
    const iconCache = getIconCache()
    const supportedExtensions = getSupportedExtensions()
    
    console.log(`🧪 Starting icon performance test with ${supportedExtensions.length} file types...`)
    
    // Clear cache to start fresh
    iconCache.clear()
    
    // Test 1: Initial render performance (cache misses)
    const renderStartTime = performance.now()
    
    for (const extension of supportedExtensions) {
      const iconDefinition = getIconDefinition(extension)
      
      // Simulate getting icon for different themes and sizes
      iconCache.getIcon(extension, iconDefinition, 'light', 'md', 'minimal')
      iconCache.getIcon(extension, iconDefinition, 'dark', 'md', 'minimal')
    }
    
    const renderEndTime = performance.now()
    const initialRenderTime = renderEndTime - renderStartTime
    
    // Test 2: Cached render performance (cache hits)
    const cachedRenderStartTime = performance.now()
    
    for (const extension of supportedExtensions) {
      const iconDefinition = getIconDefinition(extension)
      iconCache.getIcon(extension, iconDefinition, 'light', 'md', 'minimal')
    }
    
    const cachedRenderEndTime = performance.now()
    const cachedRenderTime = cachedRenderEndTime - cachedRenderStartTime
    
    // Get cache statistics
    const cacheStats = iconCache.getStats()
    
    const totalTime = performance.now() - startTime
    const averageRenderTime = initialRenderTime / supportedExtensions.length
    
    const result: PerformanceTestResult = {
      totalExtensions: supportedExtensions.length,
      renderTime: initialRenderTime,
      cacheHitRate: cacheStats.hitRate,
      memoryUsage: cacheStats.memoryUsage,
      averageRenderTime,
      success: true,
      errors
    }
    
    console.log(`✅ Icon performance test completed in ${totalTime.toFixed(2)}ms`)
    console.log(`📊 Results:`)
    console.log(`   - Total file types: ${result.totalExtensions}`)
    console.log(`   - Initial render time: ${result.renderTime.toFixed(2)}ms`)
    console.log(`   - Cached render time: ${cachedRenderTime.toFixed(2)}ms`)
    console.log(`   - Average render time: ${result.averageRenderTime.toFixed(2)}ms per icon`)
    console.log(`   - Cache hit rate: ${result.cacheHitRate.toFixed(1)}%`)
    console.log(`   - Memory usage: ${(result.memoryUsage / 1024).toFixed(1)}KB`)
    
    // Performance assertions
    if (averageRenderTime > 1.0) {
      errors.push(`Average render time too high: ${averageRenderTime.toFixed(2)}ms (target: <1ms)`)
    }
    
    if (cacheStats.memoryUsage > 500 * 1024) { // 500KB limit
      errors.push(`Memory usage too high: ${(cacheStats.memoryUsage / 1024).toFixed(1)}KB (target: <500KB)`)
    }
    
    if (errors.length > 0) {
      result.success = false
      result.errors = errors
      console.warn(`⚠️ Performance test completed with warnings:`, errors)
    }
    
    return result
    
  } catch (error) {
    const errorMessage = error instanceof Error ? error.message : String(error)
    errors.push(`Test execution failed: ${errorMessage}`)
    
    return {
      totalExtensions: 0,
      renderTime: 0,
      cacheHitRate: 0,
      memoryUsage: 0,
      averageRenderTime: 0,
      success: false,
      errors
    }
  }
}

/**
 * Test icon system with large file list simulation
 */
export async function testLargeFileListPerformance(fileCount: number = 1000): Promise<{
  renderTime: number
  success: boolean
  errors: string[]
}> {
  const errors: string[] = []
  
  try {
    console.log(`🧪 Testing large file list performance with ${fileCount} files...`)
    
    const iconCache = getIconCache()
    const supportedExtensions = getSupportedExtensions()
    
    // Generate random file list
    const files = Array.from({ length: fileCount }, (_, i) => {
      const randomExt = supportedExtensions[Math.floor(Math.random() * supportedExtensions.length)]
      return `file${i}.${randomExt}`
    })
    
    // Measure render time for large file list
    const startTime = performance.now()
    
    for (const filename of files) {
      const extension = filename.split('.').pop() || ''
      const iconDefinition = getIconDefinition(extension)
      iconCache.getIcon(extension, iconDefinition, 'light', 'md', 'minimal')
    }
    
    const endTime = performance.now()
    const renderTime = endTime - startTime
    
    console.log(`✅ Large file list test completed in ${renderTime.toFixed(2)}ms`)
    console.log(`📊 Performance: ${(renderTime / fileCount).toFixed(3)}ms per file`)
    
    // Performance assertion
    const targetTimePerFile = 0.1 // 0.1ms per file
    if ((renderTime / fileCount) > targetTimePerFile) {
      errors.push(`Render time per file too high: ${(renderTime / fileCount).toFixed(3)}ms (target: <${targetTimePerFile}ms)`)
    }
    
    return {
      renderTime,
      success: errors.length === 0,
      errors
    }
    
  } catch (error) {
    const errorMessage = error instanceof Error ? error.message : String(error)
    errors.push(`Large file list test failed: ${errorMessage}`)
    
    return {
      renderTime: 0,
      success: false,
      errors
    }
  }
}

/**
 * Test theme switching performance
 */
export async function testThemeSwitchingPerformance(): Promise<{
  switchTime: number
  success: boolean
  errors: string[]
}> {
  const errors: string[] = []
  
  try {
    console.log(`🧪 Testing theme switching performance...`)
    
    const iconCache = getIconCache()
    const supportedExtensions = getSupportedExtensions().slice(0, 20) // Test with subset
    const themes = ['light', 'dark', 'high-contrast']
    
    const startTime = performance.now()
    
    // Test switching between themes
    for (const theme of themes) {
      for (const extension of supportedExtensions) {
        const iconDefinition = getIconDefinition(extension)
        iconCache.getIcon(extension, iconDefinition, theme, 'md', 'minimal')
      }
    }
    
    const endTime = performance.now()
    const switchTime = endTime - startTime
    
    console.log(`✅ Theme switching test completed in ${switchTime.toFixed(2)}ms`)
    
    return {
      switchTime,
      success: true,
      errors
    }
    
  } catch (error) {
    const errorMessage = error instanceof Error ? error.message : String(error)
    errors.push(`Theme switching test failed: ${errorMessage}`)
    
    return {
      switchTime: 0,
      success: false,
      errors
    }
  }
}

/**
 * Run all performance tests
 */
export async function runAllPerformanceTests(): Promise<{
  iconTest: PerformanceTestResult
  largeFileTest: { renderTime: number; success: boolean; errors: string[] }
  themeTest: { switchTime: number; success: boolean; errors: string[] }
  overallSuccess: boolean
}> {
  console.log(`🚀 Running comprehensive icon system performance tests...`)
  
  const iconTest = await runIconPerformanceTest()
  const largeFileTest = await testLargeFileListPerformance(1000)
  const themeTest = await testThemeSwitchingPerformance()
  
  const overallSuccess = iconTest.success && largeFileTest.success && themeTest.success
  
  console.log(`${overallSuccess ? '✅' : '❌'} All performance tests completed`)
  
  return {
    iconTest,
    largeFileTest,
    themeTest,
    overallSuccess
  }
}
