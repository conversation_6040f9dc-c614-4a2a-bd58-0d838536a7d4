/**
 * File Type Icons - Main Export
 * Centralized exports for the enhanced icon system
 */

// Main components
export { 
  FileTypeIcons, 
  FileTypeIcon, 
  IconPreloader, 
  IconCacheStats 
} from './FileTypeIcons'

// Icon mapping and utilities
export { 
  FILE_TYPE_MAPPINGS, 
  ICON_THEMES, 
  getIconDefinition, 
  getSupportedExtensions, 
  getExtensionsByCategory 
} from './IconMapping'

// Theme system
export { 
  LIGHT_THEME, 
  DARK_THEME, 
  HIGH_CONTRAST_THEME, 
  AVAILABLE_THEMES, 
  getTheme, 
  getThemeNames, 
  getSystemTheme, 
  getThemedColor 
} from './IconThemes'

// Cache system
export { 
  IconCache, 
  getIconCache, 
  destroyIconCache, 
  useIconCache 
} from './IconCache'

// Utility functions
export {
  useFileTypeInfo,
  getFileExtension,
  isImageFile,
  isVideoFile,
  isAudioFile,
  isCodeFile,
  isDataFile
} from './FileTypeIcons'

// Types
export type { 
  IconDefinition 
} from './IconMapping'

export type { 
  IconTheme, 
  IconThemeColors 
} from './IconThemes'

export type { 
  CacheEntry, 
  CacheStats, 
  IconCacheConfig 
} from './IconCache'

export type { 
  FileTypeIconProps 
} from './FileTypeIcons'
