/**
 * Error State Manager
 * Manages error states for files and folders with real-time updates
 */

import {
  FileErrorState,
  FolderErrorState,
  FileError,
  ErrorDetectionResult,
  ErrorStateSubscription,
  ErrorVisualizationConfig,
  DEFAULT_ERROR_CONFIG,
  getHighestSeverity,
  getErrorCountBySeverity
} from './types'

export class ErrorStateManager {
  private fileErrorStates = new Map<string, FileErrorState>()
  private folderErrorStates = new Map<string, FolderErrorState>()
  private subscriptions = new Map<string, ErrorStateSubscription>()
  private config: ErrorVisualizationConfig
  private updateTimer?: NodeJS.Timeout
  private pendingUpdates = new Set<string>()

  constructor(config: Partial<ErrorVisualizationConfig> = {}) {
    this.config = { ...DEFAULT_ERROR_CONFIG, ...config }
    this.startUpdateTimer()
  }

  /**
   * Update error state for a file
   */
  updateFileErrorState(result: ErrorDetectionResult): void {
    const { filePath, errors, warnings, info, hints } = result
    
    const allErrors = [...errors, ...warnings, ...info, ...hints]
    const hasErrors = allErrors.length > 0
    
    const errorState: FileErrorState = {
      filePath,
      hasErrors,
      errorTypes: [...new Set(allErrors.map(error => error.type))],
      errorCount: errors.length,
      warningCount: warnings.length,
      infoCount: info.length,
      hintCount: hints.length,
      lastUpdated: Date.now(),
      severity: getHighestSeverity(allErrors),
      errors: allErrors
    }

    this.fileErrorStates.set(filePath, errorState)
    this.pendingUpdates.add(filePath)
    
    // Update parent folder states
    this.updateParentFolderStates(filePath)
    
    // Notify subscribers
    this.notifyFileSubscribers(filePath, errorState)
  }

  /**
   * Get error state for a file
   */
  getFileErrorState(filePath: string): FileErrorState | null {
    return this.fileErrorStates.get(filePath) || null
  }

  /**
   * Get error state for a folder
   */
  getFolderErrorState(folderPath: string): FolderErrorState | null {
    return this.folderErrorStates.get(folderPath) || null
  }

  /**
   * Clear error state for a file
   */
  clearFileErrorState(filePath: string): void {
    this.fileErrorStates.delete(filePath)
    this.updateParentFolderStates(filePath)
    
    // Notify subscribers with empty state
    const emptyState: FileErrorState = {
      filePath,
      hasErrors: false,
      errorTypes: [],
      errorCount: 0,
      warningCount: 0,
      infoCount: 0,
      hintCount: 0,
      lastUpdated: Date.now(),
      severity: 'hint',
      errors: []
    }
    
    this.notifyFileSubscribers(filePath, emptyState)
  }

  /**
   * Subscribe to error state changes for a file
   */
  subscribeToFile(
    filePath: string,
    callback: (state: FileErrorState) => void
  ): () => void {
    const id = `file-${filePath}-${Date.now()}-${Math.random()}`
    
    this.subscriptions.set(id, {
      id,
      callback: callback as any,
      filePath
    })

    // Send current state immediately
    const currentState = this.getFileErrorState(filePath)
    if (currentState) {
      callback(currentState)
    }

    return () => {
      this.subscriptions.delete(id)
    }
  }

  /**
   * Subscribe to error state changes for a folder
   */
  subscribeToFolder(
    folderPath: string,
    callback: (state: FolderErrorState) => void
  ): () => void {
    const id = `folder-${folderPath}-${Date.now()}-${Math.random()}`
    
    this.subscriptions.set(id, {
      id,
      callback: callback as any,
      folderPath
    })

    // Send current state immediately
    const currentState = this.getFolderErrorState(folderPath)
    if (currentState) {
      callback(currentState)
    }

    return () => {
      this.subscriptions.delete(id)
    }
  }

  /**
   * Get all files with errors
   */
  getFilesWithErrors(): string[] {
    return Array.from(this.fileErrorStates.entries())
      .filter(([, state]) => state.hasErrors)
      .map(([filePath]) => filePath)
  }

  /**
   * Get all folders with errors
   */
  getFoldersWithErrors(): string[] {
    return Array.from(this.folderErrorStates.entries())
      .filter(([, state]) => state.hasErrors)
      .map(([folderPath]) => folderPath)
  }

  /**
   * Get error statistics
   */
  getErrorStatistics(): {
    totalFiles: number
    filesWithErrors: number
    totalErrors: number
    totalWarnings: number
    totalInfo: number
    totalHints: number
  } {
    const states = Array.from(this.fileErrorStates.values())
    
    return {
      totalFiles: states.length,
      filesWithErrors: states.filter(state => state.hasErrors).length,
      totalErrors: states.reduce((sum, state) => sum + state.errorCount, 0),
      totalWarnings: states.reduce((sum, state) => sum + state.warningCount, 0),
      totalInfo: states.reduce((sum, state) => sum + state.infoCount, 0),
      totalHints: states.reduce((sum, state) => sum + state.hintCount, 0)
    }
  }

  /**
   * Update configuration
   */
  updateConfig(newConfig: Partial<ErrorVisualizationConfig>): void {
    this.config = { ...this.config, ...newConfig }
  }

  /**
   * Get current configuration
   */
  getConfig(): ErrorVisualizationConfig {
    return { ...this.config }
  }

  /**
   * Clear all error states
   */
  clearAllStates(): void {
    this.fileErrorStates.clear()
    this.folderErrorStates.clear()
    this.pendingUpdates.clear()
  }

  /**
   * Destroy the manager and cleanup resources
   */
  destroy(): void {
    if (this.updateTimer) {
      clearInterval(this.updateTimer)
      this.updateTimer = undefined
    }
    
    this.clearAllStates()
    this.subscriptions.clear()
  }

  /**
   * Update parent folder states based on child file changes
   */
  private updateParentFolderStates(filePath: string): void {
    const pathParts = filePath.split(/[/\\]/)
    
    // Update all parent folders
    for (let i = pathParts.length - 1; i > 0; i--) {
      const folderPath = pathParts.slice(0, i).join('/')
      this.updateFolderErrorState(folderPath)
    }
  }

  /**
   * Update error state for a folder based on its children
   */
  private updateFolderErrorState(folderPath: string): void {
    const childStates = Array.from(this.fileErrorStates.entries())
      .filter(([filePath]) => filePath.startsWith(folderPath + '/'))
      .map(([, state]) => state)

    const childErrors = new Map<string, FileErrorState>()
    childStates.forEach(state => {
      childErrors.set(state.filePath, state)
    })

    const hasErrors = childStates.some(state => state.hasErrors)
    const totalErrors = childStates.reduce((sum, state) => sum + state.errorCount, 0)
    const totalWarnings = childStates.reduce((sum, state) => sum + state.warningCount, 0)
    const totalInfo = childStates.reduce((sum, state) => sum + state.infoCount, 0)
    const totalHints = childStates.reduce((sum, state) => sum + state.hintCount, 0)
    const affectedFiles = childStates.filter(state => state.hasErrors).length

    // Determine overall severity
    let severity: 'error' | 'warning' | 'info' | 'hint' = 'hint'
    if (totalErrors > 0) severity = 'error'
    else if (totalWarnings > 0) severity = 'warning'
    else if (totalInfo > 0) severity = 'info'

    const folderState: FolderErrorState = {
      folderPath,
      hasErrors,
      totalErrors,
      totalWarnings,
      totalInfo,
      totalHints,
      affectedFiles,
      lastUpdated: Date.now(),
      severity,
      childErrors
    }

    this.folderErrorStates.set(folderPath, folderState)
    this.notifyFolderSubscribers(folderPath, folderState)
  }

  /**
   * Notify file subscribers
   */
  private notifyFileSubscribers(filePath: string, state: FileErrorState): void {
    this.subscriptions.forEach(subscription => {
      if (subscription.filePath === filePath) {
        try {
          subscription.callback(state)
        } catch (error) {
          console.error('Error notifying file error subscriber:', error)
        }
      }
    })
  }

  /**
   * Notify folder subscribers
   */
  private notifyFolderSubscribers(folderPath: string, state: FolderErrorState): void {
    this.subscriptions.forEach(subscription => {
      if (subscription.folderPath === folderPath) {
        try {
          subscription.callback(state)
        } catch (error) {
          console.error('Error notifying folder error subscriber:', error)
        }
      }
    })
  }

  /**
   * Start update timer for batched updates
   */
  private startUpdateTimer(): void {
    this.updateTimer = setInterval(() => {
      if (this.pendingUpdates.size > 0) {
        // Process pending updates
        this.pendingUpdates.clear()
      }
    }, 100) // Update every 100ms
  }
}

/**
 * Global error state manager instance
 */
let globalErrorStateManager: ErrorStateManager | null = null

/**
 * Get or create global error state manager
 */
export function getErrorStateManager(config?: Partial<ErrorVisualizationConfig>): ErrorStateManager {
  if (!globalErrorStateManager) {
    globalErrorStateManager = new ErrorStateManager(config)
  }
  return globalErrorStateManager
}

/**
 * Destroy global error state manager
 */
export function destroyErrorStateManager(): void {
  if (globalErrorStateManager) {
    globalErrorStateManager.destroy()
    globalErrorStateManager = null
  }
}

/**
 * React hook for using error state manager
 */
export function useErrorStateManager(config?: Partial<ErrorVisualizationConfig>): ErrorStateManager {
  return getErrorStateManager(config)
}
