/**
 * File Error Tracker
 * Integrates with existing error detection systems and provides real-time error tracking
 */

import { ErrorStateManager } from './ErrorStateManager'
import {
  FileError,
  ErrorDetectionResult,
  ErrorType,
  ErrorSeverity
} from './types'

export interface ErrorDetectorIntegration {
  onErrorDetected: (callback: (result: ErrorDetectionResult) => void) => void
  onErrorCleared: (callback: (filePath: string) => void) => void
  getErrorsForFile: (filePath: string) => Promise<FileError[]>
}

export class FileErrorTracker {
  private errorStateManager: ErrorStateManager
  private integrations: Map<string, ErrorDetectorIntegration> = new Map()
  private pollingInterval?: NodeJS.Timeout
  private isPolling = false

  constructor(errorStateManager: ErrorStateManager) {
    this.errorStateManager = errorStateManager
  }

  /**
   * Register an error detector integration
   */
  registerIntegration(name: string, integration: ErrorDetectorIntegration): void {
    this.integrations.set(name, integration)
    
    // Set up event listeners
    integration.onErrorDetected((result) => {
      this.handleErrorDetected(name, result)
    })
    
    integration.onErrorCleared((filePath) => {
      this.handleErrorCleared(filePath)
    })
  }

  /**
   * Unregister an error detector integration
   */
  unregisterIntegration(name: string): void {
    this.integrations.delete(name)
  }

  /**
   * Start real-time error tracking
   */
  startTracking(): void {
    if (this.isPolling) return
    
    this.isPolling = true
    this.startPolling()
    
    console.log('🔍 File error tracking started')
  }

  /**
   * Stop real-time error tracking
   */
  stopTracking(): void {
    if (!this.isPolling) return
    
    this.isPolling = false
    
    if (this.pollingInterval) {
      clearInterval(this.pollingInterval)
      this.pollingInterval = undefined
    }
    
    console.log('⏹️ File error tracking stopped')
  }

  /**
   * Manually check errors for a specific file
   */
  async checkFileErrors(filePath: string): Promise<void> {
    const allErrors: FileError[] = []
    
    // Collect errors from all integrations
    for (const [name, integration] of this.integrations) {
      try {
        const errors = await integration.getErrorsForFile(filePath)
        allErrors.push(...errors)
      } catch (error) {
        console.warn(`Failed to get errors from ${name} for ${filePath}:`, error)
      }
    }
    
    // Convert to detection result format
    const result = this.convertToDetectionResult(filePath, allErrors)
    this.errorStateManager.updateFileErrorState(result)
  }

  /**
   * Get current error statistics
   */
  getErrorStatistics() {
    return this.errorStateManager.getErrorStatistics()
  }

  /**
   * Clear all error states
   */
  clearAllErrors(): void {
    this.errorStateManager.clearAllStates()
  }

  /**
   * Destroy the tracker and cleanup resources
   */
  destroy(): void {
    this.stopTracking()
    this.integrations.clear()
  }

  /**
   * Handle error detected event from integration
   */
  private handleErrorDetected(source: string, result: ErrorDetectionResult): void {
    // Add source information to the result
    const enhancedResult = {
      ...result,
      source
    }
    
    this.errorStateManager.updateFileErrorState(enhancedResult)
  }

  /**
   * Handle error cleared event
   */
  private handleErrorCleared(filePath: string): void {
    this.errorStateManager.clearFileErrorState(filePath)
  }

  /**
   * Convert file errors to detection result format
   */
  private convertToDetectionResult(filePath: string, errors: FileError[]): ErrorDetectionResult {
    const errorsByType = {
      errors: errors.filter(e => e.severity === 'error'),
      warnings: errors.filter(e => e.severity === 'warning'),
      info: errors.filter(e => e.severity === 'info'),
      hints: errors.filter(e => e.severity === 'hint')
    }
    
    return {
      filePath,
      errors: errorsByType.errors,
      warnings: errorsByType.warnings,
      info: errorsByType.info,
      hints: errorsByType.hints,
      timestamp: Date.now(),
      source: 'file-tracker'
    }
  }

  /**
   * Start polling for error updates
   */
  private startPolling(): void {
    // Poll every 2 seconds for error updates
    this.pollingInterval = setInterval(async () => {
      if (!this.isPolling) return
      
      // This would typically poll the file system or error detection services
      // For now, we'll implement a placeholder that could integrate with existing systems
      await this.pollForErrors()
    }, 2000)
  }

  /**
   * Poll for error updates (placeholder implementation)
   */
  private async pollForErrors(): Promise<void> {
    // This is a placeholder implementation
    // In a real system, this would:
    // 1. Get list of currently open/watched files
    // 2. Check each file for errors using registered integrations
    // 3. Update error states accordingly
    
    // For demonstration, we'll create some mock error detection
    if (process.env.NODE_ENV === 'development') {
      // Only run mock detection in development
      await this.mockErrorDetection()
    }
  }

  /**
   * Mock error detection for demonstration purposes
   */
  private async mockErrorDetection(): Promise<void> {
    // This is purely for demonstration and should be removed in production
    const mockFiles = [
      'src/components/example.tsx',
      'src/utils/helper.ts',
      'src/styles/main.css'
    ]
    
    for (const filePath of mockFiles) {
      // Randomly generate some mock errors
      if (Math.random() < 0.1) { // 10% chance of having errors
        const mockErrors: FileError[] = []
        
        if (Math.random() < 0.5) {
          mockErrors.push({
            id: `mock-${Date.now()}-1`,
            filePath,
            line: Math.floor(Math.random() * 100) + 1,
            column: Math.floor(Math.random() * 50) + 1,
            message: 'Mock syntax error for demonstration',
            type: 'syntax',
            severity: 'error',
            source: 'mock-detector',
            timestamp: Date.now()
          })
        }
        
        if (Math.random() < 0.3) {
          mockErrors.push({
            id: `mock-${Date.now()}-2`,
            filePath,
            line: Math.floor(Math.random() * 100) + 1,
            column: Math.floor(Math.random() * 50) + 1,
            message: 'Mock linting warning for demonstration',
            type: 'lint',
            severity: 'warning',
            source: 'mock-linter',
            timestamp: Date.now()
          })
        }
        
        if (mockErrors.length > 0) {
          const result = this.convertToDetectionResult(filePath, mockErrors)
          this.errorStateManager.updateFileErrorState(result)
        }
      }
    }
  }
}

/**
 * Create a mock error detector integration for testing
 */
export function createMockErrorDetectorIntegration(): ErrorDetectorIntegration {
  const callbacks: Array<(result: ErrorDetectionResult) => void> = []
  const clearCallbacks: Array<(filePath: string) => void> = []
  
  return {
    onErrorDetected: (callback) => {
      callbacks.push(callback)
    },
    
    onErrorCleared: (callback) => {
      clearCallbacks.push(callback)
    },
    
    getErrorsForFile: async (filePath: string) => {
      // Mock implementation - return random errors
      const errors: FileError[] = []
      
      if (Math.random() < 0.2) { // 20% chance of errors
        errors.push({
          id: `mock-${Date.now()}`,
          filePath,
          line: Math.floor(Math.random() * 100) + 1,
          column: Math.floor(Math.random() * 50) + 1,
          message: `Mock error in ${filePath}`,
          type: 'syntax',
          severity: 'error',
          source: 'mock-detector',
          timestamp: Date.now()
        })
      }
      
      return errors
    }
  }
}

/**
 * Integration with existing ErrorDetector system
 */
export async function createErrorDetectorIntegration(): Promise<ErrorDetectorIntegration> {
  try {
    // Try to import the existing error detector
    const { ErrorDetector } = await import('../../background/error-detector')
    
    // Create integration wrapper
    return {
      onErrorDetected: (callback) => {
        // This would need to be implemented based on the actual ErrorDetector API
        console.log('Setting up error detection callback')
      },
      
      onErrorCleared: (callback) => {
        // This would need to be implemented based on the actual ErrorDetector API
        console.log('Setting up error cleared callback')
      },
      
      getErrorsForFile: async (filePath: string) => {
        // This would need to be implemented based on the actual ErrorDetector API
        console.log('Getting errors for file:', filePath)
        return []
      }
    }
  } catch (error) {
    console.warn('Could not load ErrorDetector, using mock integration:', error)
    return createMockErrorDetectorIntegration()
  }
}

/**
 * Global file error tracker instance
 */
let globalFileErrorTracker: FileErrorTracker | null = null

/**
 * Get or create global file error tracker
 */
export function getFileErrorTracker(errorStateManager?: ErrorStateManager): FileErrorTracker {
  if (!globalFileErrorTracker) {
    const manager = errorStateManager || new ErrorStateManager()
    globalFileErrorTracker = new FileErrorTracker(manager)
  }
  return globalFileErrorTracker
}

/**
 * Destroy global file error tracker
 */
export function destroyFileErrorTracker(): void {
  if (globalFileErrorTracker) {
    globalFileErrorTracker.destroy()
    globalFileErrorTracker = null
  }
}
