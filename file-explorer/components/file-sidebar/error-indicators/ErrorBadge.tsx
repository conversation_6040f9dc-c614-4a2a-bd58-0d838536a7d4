/**
 * Error Badge Component
 * Visual indicator for file and folder error states
 */

import React, { useMemo } from 'react'
import { cn } from '@/lib/utils'
import {
  AlertCircle,
  AlertTriangle,
  Info,
  HelpCircle,
  X,
  CheckCircle
} from 'lucide-react'
import {
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from '@/components/ui/tooltip'
import {
  ErrorBadgeProps,
  FileErrorState,
  FolderErrorState,
  ErrorSeverity,
  ErrorTooltipData,
  getErrorColor,
  getErrorBackground,
  shouldShowErrorBadge,
  DEFAULT_ERROR_CONFIG
} from './types'

/**
 * Size mappings for different badge sizes
 */
const SIZE_CLASSES = {
  xs: 'w-2 h-2 text-xs',
  sm: 'w-3 h-3 text-xs',
  md: 'w-4 h-4 text-xs',
  lg: 'w-5 h-5 text-sm'
} as const

/**
 * Position mappings for badge placement
 */
const POSITION_CLASSES = {
  inline: 'relative',
  overlay: 'absolute -top-1 -right-1',
  trailing: 'ml-auto'
} as const

/**
 * Get icon component for error severity
 */
function getErrorIcon(severity: ErrorSeverity): React.ComponentType<any> {
  switch (severity) {
    case 'error':
      return AlertCircle
    case 'warning':
      return AlertTriangle
    case 'info':
      return Info
    case 'hint':
      return HelpCircle
    default:
      return AlertCircle
  }
}

/**
 * Generate tooltip data from error state
 */
function generateTooltipData(errorState: FileErrorState | FolderErrorState): ErrorTooltipData {
  const isFolder = 'folderPath' in errorState
  
  if (isFolder) {
    const folderState = errorState as FolderErrorState
    return {
      title: `Folder: ${folderState.folderPath.split('/').pop() || 'Root'}`,
      errors: [
        { type: 'syntax', severity: 'error', count: folderState.totalErrors },
        { type: 'lint', severity: 'warning', count: folderState.totalWarnings },
        { type: 'type', severity: 'info', count: folderState.totalInfo },
        { type: 'runtime', severity: 'hint', count: folderState.totalHints }
      ].filter(item => item.count > 0),
      totalCount: folderState.totalErrors + folderState.totalWarnings + folderState.totalInfo + folderState.totalHints,
      lastUpdated: folderState.lastUpdated
    }
  } else {
    const fileState = errorState as FileErrorState
    return {
      title: `File: ${fileState.filePath.split('/').pop() || 'Unknown'}`,
      errors: [
        { type: 'syntax', severity: 'error', count: fileState.errorCount },
        { type: 'lint', severity: 'warning', count: fileState.warningCount },
        { type: 'type', severity: 'info', count: fileState.infoCount },
        { type: 'runtime', severity: 'hint', count: fileState.hintCount }
      ].filter(item => item.count > 0),
      totalCount: fileState.errorCount + fileState.warningCount + fileState.infoCount + fileState.hintCount,
      lastUpdated: fileState.lastUpdated
    }
  }
}

/**
 * Error Badge Component
 */
export const ErrorBadge: React.FC<ErrorBadgeProps> = ({
  errorState,
  size = 'sm',
  position = 'trailing',
  showCount = true,
  showTooltip = true,
  animated = true,
  onClick
}) => {
  // Check if badge should be shown
  const shouldShow = useMemo(() => {
    return shouldShowErrorBadge(errorState, DEFAULT_ERROR_CONFIG)
  }, [errorState])

  // Generate tooltip data
  const tooltipData = useMemo(() => {
    return generateTooltipData(errorState)
  }, [errorState])

  // Get error count to display
  const displayCount = useMemo(() => {
    const isFolder = 'folderPath' in errorState
    if (isFolder) {
      const folderState = errorState as FolderErrorState
      return folderState.totalErrors + folderState.totalWarnings
    } else {
      const fileState = errorState as FileErrorState
      return fileState.errorCount + fileState.warningCount
    }
  }, [errorState])

  // Generate styles
  const badgeStyles = useMemo(() => {
    const theme = 'light' // TODO: Get from theme context
    const color = getErrorColor(errorState.severity, theme)
    const backgroundColor = getErrorBackground(errorState.severity, theme)
    
    return {
      color,
      backgroundColor: position === 'overlay' ? backgroundColor : 'transparent',
      borderColor: color
    }
  }, [errorState.severity, position])

  // Generate CSS classes
  const badgeClasses = useMemo(() => {
    return cn(
      'flex items-center justify-center rounded-full border flex-shrink-0',
      SIZE_CLASSES[size],
      POSITION_CLASSES[position],
      animated && 'transition-all duration-200',
      onClick && 'cursor-pointer hover:scale-110',
      position === 'overlay' && 'z-10'
    )
  }, [size, position, animated, onClick])

  if (!shouldShow) return null

  const IconComponent = getErrorIcon(errorState.severity)

  const badgeContent = (
    <div
      className={badgeClasses}
      style={badgeStyles}
      onClick={onClick}
      role={onClick ? 'button' : undefined}
      tabIndex={onClick ? 0 : undefined}
      onKeyDown={onClick ? (e) => {
        if (e.key === 'Enter' || e.key === ' ') {
          e.preventDefault()
          onClick()
        }
      } : undefined}
    >
      {showCount && displayCount > 0 ? (
        <span className="font-bold text-xs">
          {displayCount > 99 ? '99+' : displayCount}
        </span>
      ) : (
        <IconComponent className="w-full h-full" />
      )}
    </div>
  )

  if (!showTooltip) {
    return badgeContent
  }

  return (
    <TooltipProvider>
      <Tooltip>
        <TooltipTrigger asChild>
          {badgeContent}
        </TooltipTrigger>
        <TooltipContent side="top" className="max-w-xs">
          <div className="space-y-2">
            <div className="font-semibold text-sm">{tooltipData.title}</div>
            
            {tooltipData.errors.length > 0 && (
              <div className="space-y-1">
                {tooltipData.errors.map((error, index) => (
                  <div key={index} className="flex items-center justify-between text-xs">
                    <span className="capitalize">{error.severity}s:</span>
                    <span className="font-medium">{error.count}</span>
                  </div>
                ))}
              </div>
            )}
            
            <div className="text-xs text-muted-foreground border-t pt-1">
              Total: {tooltipData.totalCount} issue{tooltipData.totalCount !== 1 ? 's' : ''}
            </div>
            
            <div className="text-xs text-muted-foreground">
              Updated: {new Date(tooltipData.lastUpdated).toLocaleTimeString()}
            </div>
          </div>
        </TooltipContent>
      </Tooltip>
    </TooltipProvider>
  )
}

/**
 * Animated Error Badge with entrance/exit animations
 */
export const AnimatedErrorBadge: React.FC<ErrorBadgeProps> = (props) => {
  const [isVisible, setIsVisible] = React.useState(false)
  
  React.useEffect(() => {
    const shouldShow = shouldShowErrorBadge(props.errorState, DEFAULT_ERROR_CONFIG)
    
    if (shouldShow && !isVisible) {
      setIsVisible(true)
    } else if (!shouldShow && isVisible) {
      // Delay hiding to allow exit animation
      const timer = setTimeout(() => setIsVisible(false), 200)
      return () => clearTimeout(timer)
    }
  }, [props.errorState, isVisible])

  if (!isVisible) return null

  return (
    <div className={cn(
      'transition-all duration-200',
      shouldShowErrorBadge(props.errorState, DEFAULT_ERROR_CONFIG)
        ? 'opacity-100 scale-100'
        : 'opacity-0 scale-75'
    )}>
      <ErrorBadge {...props} animated={true} />
    </div>
  )
}

/**
 * Error Summary Badge for folders with multiple error types
 */
export const ErrorSummaryBadge: React.FC<{
  folderState: FolderErrorState
  size?: 'xs' | 'sm' | 'md' | 'lg'
  onClick?: () => void
}> = ({ folderState, size = 'sm', onClick }) => {
  const summaryData = useMemo(() => {
    const { totalErrors, totalWarnings, totalInfo, totalHints } = folderState
    const total = totalErrors + totalWarnings + totalInfo + totalHints
    
    return {
      total,
      breakdown: [
        { severity: 'error' as ErrorSeverity, count: totalErrors },
        { severity: 'warning' as ErrorSeverity, count: totalWarnings },
        { severity: 'info' as ErrorSeverity, count: totalInfo },
        { severity: 'hint' as ErrorSeverity, count: totalHints }
      ].filter(item => item.count > 0)
    }
  }, [folderState])

  if (summaryData.total === 0) return null

  return (
    <div className="flex items-center gap-1">
      {summaryData.breakdown.map((item, index) => (
        <ErrorBadge
          key={index}
          errorState={{
            ...folderState,
            severity: item.severity
          }}
          size={size}
          position="inline"
          showCount={true}
          showTooltip={false}
          onClick={onClick}
        />
      ))}
    </div>
  )
}

/**
 * Error Progress Indicator
 */
export const ErrorProgressIndicator: React.FC<{
  current: number
  total: number
  severity: ErrorSeverity
  size?: 'sm' | 'md' | 'lg'
}> = ({ current, total, severity, size = 'md' }) => {
  const percentage = total > 0 ? (current / total) * 100 : 0
  const theme = 'light' // TODO: Get from theme context
  const color = getErrorColor(severity, theme)
  
  const sizeClasses = {
    sm: 'h-1',
    md: 'h-2',
    lg: 'h-3'
  }

  return (
    <div className="flex items-center gap-2">
      <div className={cn('flex-1 bg-gray-200 rounded-full overflow-hidden', sizeClasses[size])}>
        <div
          className="h-full transition-all duration-300 ease-out"
          style={{
            width: `${percentage}%`,
            backgroundColor: color
          }}
        />
      </div>
      <span className="text-xs text-muted-foreground min-w-0">
        {current}/{total}
      </span>
    </div>
  )
}

export default ErrorBadge
