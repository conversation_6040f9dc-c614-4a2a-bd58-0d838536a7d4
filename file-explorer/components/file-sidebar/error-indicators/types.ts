/**
 * Error Indicators Types and Interfaces
 * Defines types for the error visualization system
 */

export type ErrorType = 'syntax' | 'type' | 'lint' | 'build' | 'git' | 'runtime'

export type ErrorSeverity = 'error' | 'warning' | 'info' | 'hint'

export interface FileError {
  id: string
  filePath: string
  line: number
  column: number
  message: string
  type: ErrorType
  severity: ErrorSeverity
  source: string // e.g., 'typescript', 'eslint', 'prettier'
  timestamp: number
}

export interface FileErrorState {
  filePath: string
  hasErrors: boolean
  errorTypes: ErrorType[]
  errorCount: number
  warningCount: number
  infoCount: number
  hintCount: number
  lastUpdated: number
  severity: ErrorSeverity
  errors: FileError[]
}

export interface FolderErrorState {
  folderPath: string
  hasErrors: boolean
  totalErrors: number
  totalWarnings: number
  totalInfo: number
  totalHints: number
  affectedFiles: number
  lastUpdated: number
  severity: ErrorSeverity
  childErrors: Map<string, FileErrorState>
}

export interface ErrorStateSubscription {
  id: string
  callback: (state: FileErrorState | FolderErrorState) => void
  filePath?: string
  folderPath?: string
}

export interface ErrorDetectionResult {
  filePath: string
  errors: FileError[]
  warnings: FileError[]
  info: FileError[]
  hints: FileError[]
  timestamp: number
  source: string
}

export interface ErrorVisualizationConfig {
  showErrorBadges: boolean
  showWarningBadges: boolean
  showInfoBadges: boolean
  showHintBadges: boolean
  colorCoding: boolean
  animateChanges: boolean
  aggregateFolderErrors: boolean
  maxErrorsToShow: number
}

export interface ErrorBadgeProps {
  errorState: FileErrorState | FolderErrorState
  size?: 'xs' | 'sm' | 'md' | 'lg'
  position?: 'inline' | 'overlay' | 'trailing'
  showCount?: boolean
  showTooltip?: boolean
  animated?: boolean
  onClick?: () => void
}

export interface ErrorTooltipData {
  title: string
  errors: Array<{
    type: ErrorType
    severity: ErrorSeverity
    count: number
    message?: string
  }>
  totalCount: number
  lastUpdated: number
}

// Error color mappings for different themes
export const ERROR_COLORS = {
  light: {
    error: '#DC2626',
    warning: '#D97706',
    info: '#2563EB',
    hint: '#059669'
  },
  dark: {
    error: '#F87171',
    warning: '#FBBF24',
    info: '#60A5FA',
    hint: '#34D399'
  },
  'high-contrast': {
    error: '#FF0000',
    warning: '#FF8000',
    info: '#0000FF',
    hint: '#008000'
  }
} as const

// Error background colors for visual indicators
export const ERROR_BACKGROUNDS = {
  light: {
    error: '#FEF2F2',
    warning: '#FFFBEB',
    info: '#EFF6FF',
    hint: '#ECFDF5'
  },
  dark: {
    error: '#7F1D1D',
    warning: '#92400E',
    info: '#1E3A8A',
    hint: '#064E3B'
  },
  'high-contrast': {
    error: '#FFE6E6',
    warning: '#FFF2E6',
    info: '#E6F3FF',
    hint: '#E6FFE6'
  }
} as const

// Error border colors for file highlighting
export const ERROR_BORDERS = {
  light: {
    error: '#FCA5A5',
    warning: '#FCD34D',
    info: '#93C5FD',
    hint: '#6EE7B7'
  },
  dark: {
    error: '#DC2626',
    warning: '#D97706',
    info: '#2563EB',
    hint: '#059669'
  },
  'high-contrast': {
    error: '#FF0000',
    warning: '#FF8000',
    info: '#0000FF',
    hint: '#008000'
  }
} as const

// Default error visualization configuration
export const DEFAULT_ERROR_CONFIG: ErrorVisualizationConfig = {
  showErrorBadges: true,
  showWarningBadges: true,
  showInfoBadges: false,
  showHintBadges: false,
  colorCoding: true,
  animateChanges: true,
  aggregateFolderErrors: true,
  maxErrorsToShow: 99
}

// Error type priorities for display
export const ERROR_TYPE_PRIORITY: Record<ErrorType, number> = {
  syntax: 1,
  type: 2,
  build: 3,
  lint: 4,
  git: 5,
  runtime: 6
}

// Error severity priorities for display
export const ERROR_SEVERITY_PRIORITY: Record<ErrorSeverity, number> = {
  error: 1,
  warning: 2,
  info: 3,
  hint: 4
}

/**
 * Utility functions for error state management
 */
export function getHighestSeverity(errors: FileError[]): ErrorSeverity {
  if (errors.length === 0) return 'hint'
  
  const severities = errors.map(error => error.severity)
  
  if (severities.includes('error')) return 'error'
  if (severities.includes('warning')) return 'warning'
  if (severities.includes('info')) return 'info'
  return 'hint'
}

export function getErrorCountBySeverity(errors: FileError[], severity: ErrorSeverity): number {
  return errors.filter(error => error.severity === severity).length
}

export function getErrorCountByType(errors: FileError[], type: ErrorType): number {
  return errors.filter(error => error.type === type).length
}

export function shouldShowErrorBadge(
  errorState: FileErrorState | FolderErrorState,
  config: ErrorVisualizationConfig
): boolean {
  if (!errorState.hasErrors) return false
  
  const { severity } = errorState
  
  switch (severity) {
    case 'error':
      return config.showErrorBadges
    case 'warning':
      return config.showWarningBadges
    case 'info':
      return config.showInfoBadges
    case 'hint':
      return config.showHintBadges
    default:
      return false
  }
}

export function getErrorColor(
  severity: ErrorSeverity,
  theme: 'light' | 'dark' | 'high-contrast' = 'light'
): string {
  return ERROR_COLORS[theme][severity]
}

export function getErrorBackground(
  severity: ErrorSeverity,
  theme: 'light' | 'dark' | 'high-contrast' = 'light'
): string {
  return ERROR_BACKGROUNDS[theme][severity]
}

export function getErrorBorder(
  severity: ErrorSeverity,
  theme: 'light' | 'dark' | 'high-contrast' = 'light'
): string {
  return ERROR_BORDERS[theme][severity]
}
