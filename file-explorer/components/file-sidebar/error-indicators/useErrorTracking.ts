/**
 * Error Tracking Hook
 * React hook for initializing and managing error tracking in the file explorer
 */

import { useEffect, useRef, useState } from 'react'
import { ErrorStateManager } from './ErrorStateManager'
import { FileErrorTracker, createErrorDetectorIntegration, createMockErrorDetectorIntegration } from './FileErrorTracker'
import { ErrorVisualizationConfig, DEFAULT_ERROR_CONFIG } from './types'

export interface UseErrorTrackingOptions {
  config?: Partial<ErrorVisualizationConfig>
  enableMockData?: boolean
  autoStart?: boolean
}

export interface ErrorTrackingState {
  isInitialized: boolean
  isTracking: boolean
  errorCount: number
  warningCount: number
  lastUpdate: number
}

/**
 * Hook for managing error tracking in the file explorer
 */
export function useErrorTracking(options: UseErrorTrackingOptions = {}) {
  const {
    config = {},
    enableMockData = process.env.NODE_ENV === 'development',
    autoStart = true
  } = options

  const [state, setState] = useState<ErrorTrackingState>({
    isInitialized: false,
    isTracking: false,
    errorCount: 0,
    warningCount: 0,
    lastUpdate: 0
  })

  const errorStateManagerRef = useRef<ErrorStateManager | null>(null)
  const fileErrorTrackerRef = useRef<FileErrorTracker | null>(null)
  const statsUpdateIntervalRef = useRef<NodeJS.Timeout | null>(null)

  // Initialize error tracking system
  useEffect(() => {
    const initializeErrorTracking = async () => {
      try {
        // Create error state manager
        const errorStateManager = new ErrorStateManager({
          ...DEFAULT_ERROR_CONFIG,
          ...config
        })
        errorStateManagerRef.current = errorStateManager

        // Create file error tracker
        const fileErrorTracker = new FileErrorTracker(errorStateManager)
        fileErrorTrackerRef.current = fileErrorTracker

        // Register error detector integrations
        if (enableMockData) {
          console.log('🧪 Registering mock error detector for development')
          const mockIntegration = createMockErrorDetectorIntegration()
          fileErrorTracker.registerIntegration('mock-detector', mockIntegration)
        }

        try {
          console.log('🔍 Attempting to register real error detector integration')
          const realIntegration = await createErrorDetectorIntegration()
          fileErrorTracker.registerIntegration('error-detector', realIntegration)
        } catch (error) {
          console.warn('Could not register real error detector, using mock only:', error)
        }

        // Start tracking if auto-start is enabled
        if (autoStart) {
          fileErrorTracker.startTracking()
          setState(prev => ({ ...prev, isTracking: true }))
        }

        setState(prev => ({ 
          ...prev, 
          isInitialized: true,
          lastUpdate: Date.now()
        }))

        console.log('✅ Error tracking system initialized')

      } catch (error) {
        console.error('❌ Failed to initialize error tracking:', error)
        setState(prev => ({ 
          ...prev, 
          isInitialized: false,
          lastUpdate: Date.now()
        }))
      }
    }

    initializeErrorTracking()

    // Cleanup on unmount
    return () => {
      if (fileErrorTrackerRef.current) {
        fileErrorTrackerRef.current.destroy()
        fileErrorTrackerRef.current = null
      }
      
      if (errorStateManagerRef.current) {
        errorStateManagerRef.current.destroy()
        errorStateManagerRef.current = null
      }

      if (statsUpdateIntervalRef.current) {
        clearInterval(statsUpdateIntervalRef.current)
        statsUpdateIntervalRef.current = null
      }
    }
  }, []) // Only run once on mount

  // Update statistics periodically
  useEffect(() => {
    if (!state.isInitialized || !errorStateManagerRef.current) return

    const updateStats = () => {
      if (errorStateManagerRef.current) {
        const stats = errorStateManagerRef.current.getErrorStatistics()
        setState(prev => ({
          ...prev,
          errorCount: stats.totalErrors,
          warningCount: stats.totalWarnings,
          lastUpdate: Date.now()
        }))
      }
    }

    // Update stats immediately
    updateStats()

    // Set up periodic updates
    statsUpdateIntervalRef.current = setInterval(updateStats, 5000) // Update every 5 seconds

    return () => {
      if (statsUpdateIntervalRef.current) {
        clearInterval(statsUpdateIntervalRef.current)
        statsUpdateIntervalRef.current = null
      }
    }
  }, [state.isInitialized])

  // Control functions
  const startTracking = () => {
    if (fileErrorTrackerRef.current && !state.isTracking) {
      fileErrorTrackerRef.current.startTracking()
      setState(prev => ({ ...prev, isTracking: true }))
    }
  }

  const stopTracking = () => {
    if (fileErrorTrackerRef.current && state.isTracking) {
      fileErrorTrackerRef.current.stopTracking()
      setState(prev => ({ ...prev, isTracking: false }))
    }
  }

  const clearAllErrors = () => {
    if (fileErrorTrackerRef.current) {
      fileErrorTrackerRef.current.clearAllErrors()
      setState(prev => ({
        ...prev,
        errorCount: 0,
        warningCount: 0,
        lastUpdate: Date.now()
      }))
    }
  }

  const checkFileErrors = async (filePath: string) => {
    if (fileErrorTrackerRef.current) {
      await fileErrorTrackerRef.current.checkFileErrors(filePath)
    }
  }

  const updateConfig = (newConfig: Partial<ErrorVisualizationConfig>) => {
    if (errorStateManagerRef.current) {
      errorStateManagerRef.current.updateConfig(newConfig)
    }
  }

  const getErrorStatistics = () => {
    if (errorStateManagerRef.current) {
      return errorStateManagerRef.current.getErrorStatistics()
    }
    return {
      totalFiles: 0,
      filesWithErrors: 0,
      totalErrors: 0,
      totalWarnings: 0,
      totalInfo: 0,
      totalHints: 0
    }
  }

  return {
    // State
    ...state,
    
    // Control functions
    startTracking,
    stopTracking,
    clearAllErrors,
    checkFileErrors,
    updateConfig,
    getErrorStatistics,
    
    // Direct access to managers (for advanced usage)
    errorStateManager: errorStateManagerRef.current,
    fileErrorTracker: fileErrorTrackerRef.current
  }
}

/**
 * Hook for getting error state for a specific file
 */
export function useFileErrorState(filePath: string) {
  const [errorState, setErrorState] = useState(null)
  const errorStateManagerRef = useRef<ErrorStateManager | null>(null)

  useEffect(() => {
    // Get the global error state manager
    import('./ErrorStateManager').then(({ getErrorStateManager }) => {
      const manager = getErrorStateManager()
      errorStateManagerRef.current = manager

      // Subscribe to error state changes for this file
      const unsubscribe = manager.subscribeToFile(filePath, (state) => {
        setErrorState(state)
      })

      return unsubscribe
    })
  }, [filePath])

  return errorState
}

/**
 * Hook for getting error state for a specific folder
 */
export function useFolderErrorState(folderPath: string) {
  const [errorState, setErrorState] = useState(null)
  const errorStateManagerRef = useRef<ErrorStateManager | null>(null)

  useEffect(() => {
    // Get the global error state manager
    import('./ErrorStateManager').then(({ getErrorStateManager }) => {
      const manager = getErrorStateManager()
      errorStateManagerRef.current = manager

      // Subscribe to error state changes for this folder
      const unsubscribe = manager.subscribeToFolder(folderPath, (state) => {
        setErrorState(state)
      })

      return unsubscribe
    })
  }, [folderPath])

  return errorState
}

/**
 * Hook for getting overall error statistics
 */
export function useErrorStatistics() {
  const [stats, setStats] = useState({
    totalFiles: 0,
    filesWithErrors: 0,
    totalErrors: 0,
    totalWarnings: 0,
    totalInfo: 0,
    totalHints: 0
  })

  useEffect(() => {
    import('./ErrorStateManager').then(({ getErrorStateManager }) => {
      const manager = getErrorStateManager()

      const updateStats = () => {
        setStats(manager.getErrorStatistics())
      }

      // Update immediately
      updateStats()

      // Set up periodic updates
      const interval = setInterval(updateStats, 2000)

      return () => clearInterval(interval)
    })
  }, [])

  return stats
}
