/**
 * Error Indicators System - Main Export
 * Centralized exports for the error visualization system
 */

// Main components
export { 
  ErrorBadge, 
  AnimatedErrorBadge, 
  ErrorSummaryBadge, 
  ErrorProgressIndicator 
} from './ErrorBadge'

// State management
export { 
  ErrorStateManager, 
  getErrorStateManager, 
  destroyErrorStateManager, 
  useErrorStateManager 
} from './ErrorStateManager'

// Error tracking
export {
  FileErrorTracker,
  getFileErrorTracker,
  destroyFileErrorTracker,
  createMockErrorDetectorIntegration,
  createErrorDetectorIntegration
} from './FileErrorTracker'

// React hooks
export {
  useErrorTracking,
  useFileErrorState,
  useFolderErrorState,
  useErrorStatistics
} from './useErrorTracking'

// Types and utilities
export type {
  ErrorType,
  ErrorSeverity,
  FileError,
  FileErrorState,
  FolderErrorState,
  ErrorDetectionResult,
  ErrorVisualizationConfig,
  ErrorBadgeProps,
  ErrorTooltipData
} from './types'

export {
  ERROR_COLORS,
  ERROR_BACKGROUNDS,
  ERROR_BORDERS,
  DEFAULT_ERROR_CONFIG,
  ERROR_TYPE_PRIORITY,
  ERROR_SEVERITY_PRIORITY,
  getHighestSeverity,
  getErrorCountBySeverity,
  getErrorCountByType,
  shouldShowErrorBadge,
  getErrorColor,
  getErrorBackground,
  getErrorBorder
} from './types'
