/**
 * Context Menu Actions Handler
 * Implements all context menu actions with proper file operations integration
 */

import { ContextMenuAction, ContextMenuActionHandler, FileOperationResult } from './types'
import { FileSystemItem } from '../types'
import { ClipboardManager } from './ClipboardManager'

export class ContextMenuActionHandler implements ContextMenuActionHandler {
  private clipboardManager: ClipboardManager

  constructor(clipboardManager: ClipboardManager) {
    this.clipboardManager = clipboardManager
  }

  /**
   * Check if action can be executed for the given item
   */
  canExecute(action: ContextMenuAction, item: FileSystemItem): boolean {
    switch (action) {
      case 'open':
      case 'copy':
      case 'cut':
      case 'rename':
      case 'delete':
      case 'copy-path':
      case 'reveal':
        return true

      case 'paste':
        return item.type === 'folder' && this.clipboardManager.canPaste()

      case 'new-file':
      case 'new-folder':
        return item.type === 'folder'

      default:
        return false
    }
  }

  /**
   * Execute the specified action
   */
  async execute(action: ContextMenuAction, item: FileSystemItem): Promise<FileOperationResult> {
    if (!this.canExecute(action, item)) {
      return {
        success: false,
        error: `Action '${action}' cannot be executed for this item`
      }
    }

    try {
      switch (action) {
        case 'open':
          return await this.handleOpen(item)
        case 'copy':
          return await this.handleCopy(item)
        case 'cut':
          return await this.handleCut(item)
        case 'paste':
          return await this.handlePaste(item)
        case 'rename':
          return await this.handleRename(item)
        case 'delete':
          return await this.handleDelete(item)
        case 'copy-path':
          return await this.handleCopyPath(item)
        case 'reveal':
          return await this.handleReveal(item)
        case 'new-file':
          return await this.handleNewFile(item)
        case 'new-folder':
          return await this.handleNewFolder(item)
        default:
          return {
            success: false,
            error: `Unknown action: ${action}`
          }
      }
    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : String(error)
      }
    }
  }

  /**
   * Handle open action
   */
  private async handleOpen(item: FileSystemItem): Promise<FileOperationResult> {
    try {
      const { globalFileOperations } = await import('../../background/file-operations')
      return await globalFileOperations.openFile(item.path || '')
    } catch (error) {
      return {
        success: false,
        error: `Failed to open file: ${error instanceof Error ? error.message : String(error)}`
      }
    }
  }



  /**
   * Handle copy action
   */
  private async handleCopy(item: FileSystemItem): Promise<FileOperationResult> {
    try {
      await this.clipboardManager.copyItem(item)
      return {
        success: true,
        data: { action: 'copy', item: item.name }
      }
    } catch (error) {
      return {
        success: false,
        error: `Failed to copy: ${error instanceof Error ? error.message : String(error)}`
      }
    }
  }

  /**
   * Handle cut action
   */
  private async handleCut(item: FileSystemItem): Promise<FileOperationResult> {
    try {
      await this.clipboardManager.cutItem(item)
      return {
        success: true,
        data: { action: 'cut', item: item.name }
      }
    } catch (error) {
      return {
        success: false,
        error: `Failed to cut: ${error instanceof Error ? error.message : String(error)}`
      }
    }
  }

  /**
   * Handle paste action
   */
  private async handlePaste(item: FileSystemItem): Promise<FileOperationResult> {
    try {
      return await this.clipboardManager.paste(item.path || '')
    } catch (error) {
      return {
        success: false,
        error: `Failed to paste: ${error instanceof Error ? error.message : String(error)}`
      }
    }
  }

  /**
   * Handle rename action
   */
  private async handleRename(item: FileSystemItem): Promise<FileOperationResult> {
    try {
      // Prompt user for new name
      const newName = prompt(`Rename "${item.name}" to:`, item.name)
      if (!newName || newName === item.name) {
        return {
          success: false,
          error: 'Rename operation cancelled or no change made'
        }
      }

      // Validate new name
      if (newName.includes('/') || newName.includes('\\') || newName.includes(':')) {
        return {
          success: false,
          error: 'Invalid filename: cannot contain / \\ or :'
        }
      }

      const { globalFileOperations } = await import('../../background/file-operations')
      const currentPath = item.path || ''
      const parentPath = currentPath.substring(0, currentPath.lastIndexOf('/'))
      const newPath = parentPath ? `${parentPath}/${newName}` : newName

      return await globalFileOperations.moveFile(currentPath, newPath)
    } catch (error) {
      return {
        success: false,
        error: `Failed to rename: ${error instanceof Error ? error.message : String(error)}`
      }
    }
  }

  /**
   * Handle delete action
   */
  private async handleDelete(item: FileSystemItem): Promise<FileOperationResult> {
    try {
      // Show confirmation dialog first
      const confirmed = await this.showDeleteConfirmation(item)
      if (!confirmed) {
        return {
          success: false,
          error: 'Delete operation cancelled by user'
        }
      }

      const { globalFileOperations } = await import('../../background/file-operations')
      return await globalFileOperations.deleteFile(item.path || '')
    } catch (error) {
      return {
        success: false,
        error: `Failed to delete: ${error instanceof Error ? error.message : String(error)}`
      }
    }
  }



  /**
   * Handle copy path action
   */
  private async handleCopyPath(item: FileSystemItem): Promise<FileOperationResult> {
    try {
      const path = item.path || ''
      if (!path) {
        return {
          success: false,
          error: 'No path available for this item'
        }
      }

      await this.clipboardManager.copyPath(path)

      // Show temporary notification
      this.showTemporaryNotification(`Path copied: ${path}`)

      return {
        success: true,
        data: { action: 'copy-path', path: path, message: 'Path copied to clipboard' }
      }
    } catch (error) {
      return {
        success: false,
        error: `Failed to copy path: ${error instanceof Error ? error.message : String(error)}`
      }
    }
  }



  /**
   * Handle reveal in explorer action
   */
  private async handleReveal(item: FileSystemItem): Promise<FileOperationResult> {
    try {
      // Use Electron's shell.showItemInFolder if available
      if (typeof window !== 'undefined' && (window as any).electronAPI) {
        await (window as any).electronAPI.showItemInFolder(item.path)
        return { success: true }
      }
      
      // Fallback for web environments
      return {
        success: false,
        error: 'Reveal in explorer not available in web environment'
      }
    } catch (error) {
      return {
        success: false,
        error: `Failed to reveal in explorer: ${error instanceof Error ? error.message : String(error)}`
      }
    }
  }



  /**
   * Handle new file action
   */
  private async handleNewFile(item: FileSystemItem): Promise<FileOperationResult> {
    try {
      const fileName = prompt('Enter file name:', 'new-file.txt')
      if (!fileName) {
        return {
          success: false,
          error: 'File creation cancelled'
        }
      }

      // Validate filename
      if (fileName.includes('/') || fileName.includes('\\') || fileName.includes(':')) {
        return {
          success: false,
          error: 'Invalid filename: cannot contain / \\ or :'
        }
      }

      const { globalFileOperations } = await import('../../background/file-operations')
      const parentPath = item.path || ''
      const filePath = parentPath ? `${parentPath}/${fileName}` : fileName

      return await globalFileOperations.createFile(filePath, '')
    } catch (error) {
      return {
        success: false,
        error: `Failed to create file: ${error instanceof Error ? error.message : String(error)}`
      }
    }
  }

  /**
   * Handle new folder action
   */
  private async handleNewFolder(item: FileSystemItem): Promise<FileOperationResult> {
    try {
      const folderName = prompt('Enter folder name:', 'new-folder')
      if (!folderName) {
        return {
          success: false,
          error: 'Folder creation cancelled'
        }
      }

      // Validate folder name
      if (folderName.includes('/') || folderName.includes('\\') || folderName.includes(':')) {
        return {
          success: false,
          error: 'Invalid folder name: cannot contain / \\ or :'
        }
      }

      const { globalFileOperations } = await import('../../background/file-operations')
      const parentPath = item.path || ''
      const folderPath = parentPath ? `${parentPath}/${folderName}` : folderName

      return await globalFileOperations.createDirectory(folderPath)
    } catch (error) {
      return {
        success: false,
        error: `Failed to create folder: ${error instanceof Error ? error.message : String(error)}`
      }
    }
  }



  /**
   * Utility methods
   */
  private async showDeleteConfirmation(item: FileSystemItem): Promise<boolean> {
    // This would show a proper confirmation dialog
    // For now, use browser confirm as fallback
    return confirm(`Are you sure you want to delete "${item.name}"?`)
  }

  private showTemporaryNotification(message: string): void {
    // Simple temporary notification - could be enhanced with a proper toast system
    console.log(`✅ ${message}`)

    // Try to show a temporary visual notification if possible
    if (typeof window !== 'undefined') {
      const notification = document.createElement('div')
      notification.textContent = message
      notification.style.cssText = `
        position: fixed;
        top: 20px;
        right: 20px;
        background: #10b981;
        color: white;
        padding: 8px 16px;
        border-radius: 6px;
        font-size: 14px;
        z-index: 10000;
        box-shadow: 0 4px 12px rgba(0,0,0,0.15);
      `

      document.body.appendChild(notification)

      setTimeout(() => {
        if (notification.parentNode) {
          notification.parentNode.removeChild(notification)
        }
      }, 2000)
    }
  }
}
