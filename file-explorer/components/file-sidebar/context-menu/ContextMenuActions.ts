/**
 * Context Menu Actions Handler
 * Implements all context menu actions with proper file operations integration
 */

import { ContextMenuAction, ContextMenuActionHandler, FileOperationResult } from './types'
import { FileSystemItem } from '../types'
import { ClipboardManager } from './ClipboardManager'

export class ContextMenuActionHandler implements ContextMenuActionHandler {
  private clipboardManager: ClipboardManager

  constructor(clipboardManager: ClipboardManager) {
    this.clipboardManager = clipboardManager
  }

  /**
   * Check if action can be executed for the given item
   */
  canExecute(action: ContextMenuAction, item: FileSystemItem): boolean {
    switch (action) {
      case 'open':
      case 'copy':
      case 'cut':
      case 'rename':
      case 'delete':
      case 'copy-path':
      case 'reveal':
        return true

      case 'paste':
        return item.type === 'folder' && this.clipboardManager.canPaste()

      case 'new-file':
      case 'new-folder':
        return item.type === 'folder'

      default:
        return false
    }
  }

  /**
   * Execute the specified action
   */
  async execute(action: ContextMenuAction, item: FileSystemItem): Promise<FileOperationResult> {
    if (!this.canExecute(action, item)) {
      return {
        success: false,
        error: `Action '${action}' cannot be executed for this item`
      }
    }

    try {
      switch (action) {
        case 'open':
          return await this.handleOpen(item)
        case 'copy':
          return await this.handleCopy(item)
        case 'cut':
          return await this.handleCut(item)
        case 'paste':
          return await this.handlePaste(item)
        case 'rename':
          return await this.handleRename(item)
        case 'delete':
          return await this.handleDelete(item)
        case 'copy-path':
          return await this.handleCopyPath(item)
        case 'reveal':
          return await this.handleReveal(item)
        case 'new-file':
          return await this.handleNewFile(item)
        case 'new-folder':
          return await this.handleNewFolder(item)
        default:
          return {
            success: false,
            error: `Unknown action: ${action}`
          }
      }
    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : String(error)
      }
    }
  }

  /**
   * Handle open action
   */
  private async handleOpen(item: FileSystemItem): Promise<FileOperationResult> {
    try {
      const filePath = item.path || ''

      // Try to open with Electron API if available
      if (typeof window !== 'undefined' && (window as any).electronAPI) {
        try {
          await (window as any).electronAPI.openFile(filePath)
          return { success: true, data: { action: 'open', path: filePath } }
        } catch (electronError) {
          console.warn('Electron open failed, trying fallback:', electronError)
        }
      }

      // Fallback: trigger file selection event for the file explorer
      // This will open the file in the editor
      if (item.type === 'file') {
        // Dispatch a custom event to notify the file explorer to select this file
        const event = new CustomEvent('file-explorer-select', {
          detail: { path: filePath, item }
        })
        window.dispatchEvent(event)

        this.showTemporaryNotification(`Opening ${item.name}`)
        return {
          success: true,
          data: { action: 'open', path: filePath, method: 'file-explorer-select' }
        }
      } else {
        return {
          success: false,
          error: 'Cannot open folders directly'
        }
      }
    } catch (error) {
      return {
        success: false,
        error: `Failed to open file: ${error instanceof Error ? error.message : String(error)}`
      }
    }
  }



  /**
   * Handle copy action
   */
  private async handleCopy(item: FileSystemItem): Promise<FileOperationResult> {
    try {
      await this.clipboardManager.copyItem(item)
      return {
        success: true,
        data: { action: 'copy', item: item.name }
      }
    } catch (error) {
      return {
        success: false,
        error: `Failed to copy: ${error instanceof Error ? error.message : String(error)}`
      }
    }
  }

  /**
   * Handle cut action
   */
  private async handleCut(item: FileSystemItem): Promise<FileOperationResult> {
    try {
      await this.clipboardManager.cutItem(item)
      return {
        success: true,
        data: { action: 'cut', item: item.name }
      }
    } catch (error) {
      return {
        success: false,
        error: `Failed to cut: ${error instanceof Error ? error.message : String(error)}`
      }
    }
  }

  /**
   * Handle paste action
   */
  private async handlePaste(item: FileSystemItem): Promise<FileOperationResult> {
    try {
      return await this.clipboardManager.paste(item.path || '')
    } catch (error) {
      return {
        success: false,
        error: `Failed to paste: ${error instanceof Error ? error.message : String(error)}`
      }
    }
  }

  /**
   * Handle rename action
   */
  private async handleRename(item: FileSystemItem): Promise<FileOperationResult> {
    try {
      // Create a simple input dialog using DOM
      const newName = await this.showInputDialog(`Rename "${item.name}"`, item.name)
      if (!newName || newName === item.name) {
        return {
          success: false,
          error: 'Rename operation cancelled or no change made'
        }
      }

      // Validate new name
      if (newName.includes('/') || newName.includes('\\') || newName.includes(':')) {
        return {
          success: false,
          error: 'Invalid filename: cannot contain / \\ or :'
        }
      }

      const { globalFileOperations } = await import('../../background/file-operations')
      const currentPath = item.path || ''
      const parentPath = currentPath.substring(0, currentPath.lastIndexOf('/'))
      const newPath = parentPath ? `${parentPath}/${newName}` : newName

      const agentId = 'context-menu-user'
      const options = { validatePath: true }
      return await globalFileOperations.moveFile(currentPath, newPath, options, agentId)
    } catch (error) {
      return {
        success: false,
        error: `Failed to rename: ${error instanceof Error ? error.message : String(error)}`
      }
    }
  }

  /**
   * Handle delete action
   */
  private async handleDelete(item: FileSystemItem): Promise<FileOperationResult> {
    try {
      // Show confirmation dialog first
      const confirmed = await this.showDeleteConfirmation(item)
      if (!confirmed) {
        return {
          success: false,
          error: 'Delete operation cancelled by user'
        }
      }

      const { globalFileOperations } = await import('../../background/file-operations')

      // Try delete operation with proper agent ID
      const agentId = 'context-menu-user' // Use a specific agent ID for user-initiated actions
      const options = {
        validatePath: true,
        backup: false // Don't backup for user delete operations
      }

      if (item.type === 'folder') {
        return await globalFileOperations.removeDirectory(item.path || '', options, agentId)
      } else {
        return await globalFileOperations.deleteFile(item.path || '', options, agentId)
      }
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : String(error)

      // Handle specific security errors
      if (errorMessage.includes('Agent authentication required')) {
        return {
          success: false,
          error: 'Delete operation requires authentication. Please ensure you have proper permissions.'
        }
      }

      return {
        success: false,
        error: `Failed to delete: ${errorMessage}`
      }
    }
  }



  /**
   * Handle copy path action
   */
  private async handleCopyPath(item: FileSystemItem): Promise<FileOperationResult> {
    try {
      const path = item.path || ''
      if (!path) {
        return {
          success: false,
          error: 'No path available for this item'
        }
      }

      await this.clipboardManager.copyPath(path)

      // Show temporary notification
      this.showTemporaryNotification(`Path copied: ${path}`)

      return {
        success: true,
        data: { action: 'copy-path', path: path, message: 'Path copied to clipboard' }
      }
    } catch (error) {
      return {
        success: false,
        error: `Failed to copy path: ${error instanceof Error ? error.message : String(error)}`
      }
    }
  }



  /**
   * Handle reveal in explorer action
   */
  private async handleReveal(item: FileSystemItem): Promise<FileOperationResult> {
    try {
      const filePath = item.path || ''

      // Try Electron API first
      if (typeof window !== 'undefined' && (window as any).electronAPI && (window as any).electronAPI.showItemInFolder) {
        try {
          await (window as any).electronAPI.showItemInFolder(filePath)
          return { success: true, data: { action: 'reveal', path: filePath } }
        } catch (electronError) {
          console.warn('Electron reveal failed, trying fallback:', electronError)
        }
      }

      // Fallback: Copy path and show notification
      await this.clipboardManager.copyPath(filePath)
      this.showTemporaryNotification(`Path copied to clipboard: ${filePath}`)

      return {
        success: true,
        data: {
          action: 'reveal-fallback',
          path: filePath,
          message: 'Path copied to clipboard (reveal not available in web environment)'
        }
      }
    } catch (error) {
      return {
        success: false,
        error: `Failed to reveal in explorer: ${error instanceof Error ? error.message : String(error)}`
      }
    }
  }



  /**
   * Handle new file action
   */
  private async handleNewFile(item: FileSystemItem): Promise<FileOperationResult> {
    try {
      const fileName = await this.showInputDialog('Enter file name:', 'new-file.txt')
      if (!fileName) {
        return {
          success: false,
          error: 'File creation cancelled'
        }
      }

      // Validate filename
      if (fileName.includes('/') || fileName.includes('\\') || fileName.includes(':')) {
        return {
          success: false,
          error: 'Invalid filename: cannot contain / \\ or :'
        }
      }

      const { globalFileOperations } = await import('../../background/file-operations')
      const parentPath = item.path || ''
      const filePath = parentPath ? `${parentPath}/${fileName}` : fileName

      const agentId = 'context-menu-user'
      const options = { validatePath: true, createDirectories: true }
      return await globalFileOperations.createFile(filePath, '', options, agentId)
    } catch (error) {
      return {
        success: false,
        error: `Failed to create file: ${error instanceof Error ? error.message : String(error)}`
      }
    }
  }

  /**
   * Handle new folder action
   */
  private async handleNewFolder(item: FileSystemItem): Promise<FileOperationResult> {
    try {
      const folderName = await this.showInputDialog('Enter folder name:', 'new-folder')
      if (!folderName) {
        return {
          success: false,
          error: 'Folder creation cancelled'
        }
      }

      // Validate folder name
      if (folderName.includes('/') || folderName.includes('\\') || folderName.includes(':')) {
        return {
          success: false,
          error: 'Invalid folder name: cannot contain / \\ or :'
        }
      }

      const { globalFileOperations } = await import('../../background/file-operations')
      const parentPath = item.path || ''
      const folderPath = parentPath ? `${parentPath}/${folderName}` : folderName

      const agentId = 'context-menu-user'
      const options = { validatePath: true, createDirectories: true }
      return await globalFileOperations.createDirectory(folderPath, options, agentId)
    } catch (error) {
      return {
        success: false,
        error: `Failed to create folder: ${error instanceof Error ? error.message : String(error)}`
      }
    }
  }



  /**
   * Utility methods
   */
  private async showDeleteConfirmation(item: FileSystemItem): Promise<boolean> {
    return this.showConfirmDialog(
      `Delete "${item.name}"?`,
      `Are you sure you want to delete "${item.name}"? This action cannot be undone.`,
      'Delete',
      'Cancel'
    )
  }

  private async showInputDialog(title: string, defaultValue: string = ''): Promise<string | null> {
    return new Promise((resolve) => {
      // Create modal overlay
      const overlay = document.createElement('div')
      overlay.style.cssText = `
        position: fixed;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background: rgba(0, 0, 0, 0.5);
        display: flex;
        align-items: center;
        justify-content: center;
        z-index: 10000;
      `

      // Create dialog
      const dialog = document.createElement('div')
      dialog.style.cssText = `
        background: white;
        border-radius: 8px;
        padding: 24px;
        min-width: 300px;
        box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1);
      `

      dialog.innerHTML = `
        <h3 style="margin: 0 0 16px 0; font-size: 18px; font-weight: 600; color: #1f2937;">${title}</h3>
        <input type="text" value="${defaultValue}" style="
          width: 100%;
          padding: 8px 12px;
          border: 1px solid #d1d5db;
          border-radius: 6px;
          font-size: 14px;
          margin-bottom: 16px;
          box-sizing: border-box;
        " />
        <div style="display: flex; gap: 8px; justify-content: flex-end;">
          <button class="cancel-btn" style="
            padding: 8px 16px;
            border: 1px solid #d1d5db;
            background: white;
            border-radius: 6px;
            cursor: pointer;
            font-size: 14px;
          ">Cancel</button>
          <button class="ok-btn" style="
            padding: 8px 16px;
            border: none;
            background: #3b82f6;
            color: white;
            border-radius: 6px;
            cursor: pointer;
            font-size: 14px;
          ">OK</button>
        </div>
      `

      const input = dialog.querySelector('input') as HTMLInputElement
      const cancelBtn = dialog.querySelector('.cancel-btn') as HTMLButtonElement
      const okBtn = dialog.querySelector('.ok-btn') as HTMLButtonElement

      const cleanup = () => {
        if (overlay.parentNode) {
          overlay.parentNode.removeChild(overlay)
        }
      }

      cancelBtn.onclick = () => {
        cleanup()
        resolve(null)
      }

      okBtn.onclick = () => {
        const value = input.value.trim()
        cleanup()
        resolve(value || null)
      }

      input.onkeydown = (e) => {
        if (e.key === 'Enter') {
          okBtn.click()
        } else if (e.key === 'Escape') {
          cancelBtn.click()
        }
      }

      overlay.onclick = (e) => {
        if (e.target === overlay) {
          cancelBtn.click()
        }
      }

      overlay.appendChild(dialog)
      document.body.appendChild(overlay)
      input.focus()
      input.select()
    })
  }

  private async showConfirmDialog(title: string, message: string, confirmText: string = 'OK', cancelText: string = 'Cancel'): Promise<boolean> {
    return new Promise((resolve) => {
      // Create modal overlay
      const overlay = document.createElement('div')
      overlay.style.cssText = `
        position: fixed;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background: rgba(0, 0, 0, 0.5);
        display: flex;
        align-items: center;
        justify-content: center;
        z-index: 10000;
      `

      // Create dialog
      const dialog = document.createElement('div')
      dialog.style.cssText = `
        background: white;
        border-radius: 8px;
        padding: 24px;
        min-width: 300px;
        max-width: 400px;
        box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1);
      `

      dialog.innerHTML = `
        <h3 style="margin: 0 0 12px 0; font-size: 18px; font-weight: 600; color: #1f2937;">${title}</h3>
        <p style="margin: 0 0 20px 0; color: #6b7280; line-height: 1.5;">${message}</p>
        <div style="display: flex; gap: 8px; justify-content: flex-end;">
          <button class="cancel-btn" style="
            padding: 8px 16px;
            border: 1px solid #d1d5db;
            background: white;
            border-radius: 6px;
            cursor: pointer;
            font-size: 14px;
          ">${cancelText}</button>
          <button class="confirm-btn" style="
            padding: 8px 16px;
            border: none;
            background: #ef4444;
            color: white;
            border-radius: 6px;
            cursor: pointer;
            font-size: 14px;
          ">${confirmText}</button>
        </div>
      `

      const cancelBtn = dialog.querySelector('.cancel-btn') as HTMLButtonElement
      const confirmBtn = dialog.querySelector('.confirm-btn') as HTMLButtonElement

      const cleanup = () => {
        if (overlay.parentNode) {
          overlay.parentNode.removeChild(overlay)
        }
      }

      cancelBtn.onclick = () => {
        cleanup()
        resolve(false)
      }

      confirmBtn.onclick = () => {
        cleanup()
        resolve(true)
      }

      overlay.onclick = (e) => {
        if (e.target === overlay) {
          cancelBtn.click()
        }
      }

      overlay.appendChild(dialog)
      document.body.appendChild(overlay)
      confirmBtn.focus()
    })
  }

  private showTemporaryNotification(message: string): void {
    // Simple temporary notification - could be enhanced with a proper toast system
    console.log(`✅ ${message}`)

    // Try to show a temporary visual notification if possible
    if (typeof window !== 'undefined') {
      const notification = document.createElement('div')
      notification.textContent = message
      notification.style.cssText = `
        position: fixed;
        top: 20px;
        right: 20px;
        background: #10b981;
        color: white;
        padding: 8px 16px;
        border-radius: 6px;
        font-size: 14px;
        z-index: 10000;
        box-shadow: 0 4px 12px rgba(0,0,0,0.15);
      `

      document.body.appendChild(notification)

      setTimeout(() => {
        if (notification.parentNode) {
          notification.parentNode.removeChild(notification)
        }
      }, 2000)
    }
  }
}
