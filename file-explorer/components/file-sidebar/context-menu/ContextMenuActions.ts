/**
 * Context Menu Actions Handler
 * Implements all context menu actions with proper file operations integration
 */

import { ContextMenuAction, ContextMenuActionHandler, FileOperationResult } from './types'
import { FileSystemItem } from '../types'
import { ClipboardManager } from './ClipboardManager'

export class ContextMenuActionHandler implements ContextMenuActionHandler {
  private clipboardManager: ClipboardManager

  constructor(clipboardManager: ClipboardManager) {
    this.clipboardManager = clipboardManager
  }

  /**
   * Check if action can be executed for the given item
   */
  canExecute(action: ContextMenuAction, item: FileSystemItem): boolean {
    switch (action) {
      case 'open':
      case 'open-with':
      case 'copy':
      case 'cut':
      case 'rename':
      case 'delete':
      case 'duplicate':
      case 'copy-path':
      case 'copy-relative-path':
      case 'reveal':
      case 'properties':
        return true

      case 'paste':
        return item.type === 'folder' && this.clipboardManager.canPaste()

      case 'new-file':
      case 'new-folder':
      case 'open-terminal':
        return item.type === 'folder'

      case 'compress':
        return true // Can compress any file or folder

      case 'extract':
        return this.isArchiveFile(item.name)

      case 'git-add':
      case 'git-commit':
        return this.isInGitRepository(item.path || '')

      default:
        return false
    }
  }

  /**
   * Execute the specified action
   */
  async execute(action: ContextMenuAction, item: FileSystemItem): Promise<FileOperationResult> {
    if (!this.canExecute(action, item)) {
      return {
        success: false,
        error: `Action '${action}' cannot be executed for this item`
      }
    }

    try {
      switch (action) {
        case 'open':
          return await this.handleOpen(item)
        case 'open-with':
          return await this.handleOpenWith(item)
        case 'copy':
          return await this.handleCopy(item)
        case 'cut':
          return await this.handleCut(item)
        case 'paste':
          return await this.handlePaste(item)
        case 'rename':
          return await this.handleRename(item)
        case 'delete':
          return await this.handleDelete(item)
        case 'duplicate':
          return await this.handleDuplicate(item)
        case 'copy-path':
          return await this.handleCopyPath(item)
        case 'copy-relative-path':
          return await this.handleCopyRelativePath(item)
        case 'reveal':
          return await this.handleReveal(item)
        case 'properties':
          return await this.handleProperties(item)
        case 'new-file':
          return await this.handleNewFile(item)
        case 'new-folder':
          return await this.handleNewFolder(item)
        case 'open-terminal':
          return await this.handleOpenTerminal(item)
        case 'compress':
          return await this.handleCompress(item)
        case 'extract':
          return await this.handleExtract(item)
        case 'git-add':
          return await this.handleGitAdd(item)
        case 'git-commit':
          return await this.handleGitCommit(item)
        default:
          return {
            success: false,
            error: `Unknown action: ${action}`
          }
      }
    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : String(error)
      }
    }
  }

  /**
   * Handle open action
   */
  private async handleOpen(item: FileSystemItem): Promise<FileOperationResult> {
    try {
      const { globalFileOperations } = await import('../../background/file-operations')
      return await globalFileOperations.openFile(item.path || '')
    } catch (error) {
      return {
        success: false,
        error: `Failed to open file: ${error instanceof Error ? error.message : String(error)}`
      }
    }
  }

  /**
   * Handle open with action
   */
  private async handleOpenWith(item: FileSystemItem): Promise<FileOperationResult> {
    // This would typically show a dialog to choose application
    // For now, return a placeholder implementation
    return {
      success: false,
      error: 'Open With dialog not yet implemented'
    }
  }

  /**
   * Handle copy action
   */
  private async handleCopy(item: FileSystemItem): Promise<FileOperationResult> {
    try {
      await this.clipboardManager.copyItem(item)
      return {
        success: true,
        data: { action: 'copy', item: item.name }
      }
    } catch (error) {
      return {
        success: false,
        error: `Failed to copy: ${error instanceof Error ? error.message : String(error)}`
      }
    }
  }

  /**
   * Handle cut action
   */
  private async handleCut(item: FileSystemItem): Promise<FileOperationResult> {
    try {
      await this.clipboardManager.cutItem(item)
      return {
        success: true,
        data: { action: 'cut', item: item.name }
      }
    } catch (error) {
      return {
        success: false,
        error: `Failed to cut: ${error instanceof Error ? error.message : String(error)}`
      }
    }
  }

  /**
   * Handle paste action
   */
  private async handlePaste(item: FileSystemItem): Promise<FileOperationResult> {
    try {
      return await this.clipboardManager.paste(item.path || '')
    } catch (error) {
      return {
        success: false,
        error: `Failed to paste: ${error instanceof Error ? error.message : String(error)}`
      }
    }
  }

  /**
   * Handle rename action
   */
  private async handleRename(item: FileSystemItem): Promise<FileOperationResult> {
    // This would typically show an inline editor or dialog
    // For now, return a placeholder that triggers the rename UI
    return {
      success: true,
      data: { action: 'rename', item: item.name, triggerUI: true }
    }
  }

  /**
   * Handle delete action
   */
  private async handleDelete(item: FileSystemItem): Promise<FileOperationResult> {
    try {
      // Show confirmation dialog first
      const confirmed = await this.showDeleteConfirmation(item)
      if (!confirmed) {
        return {
          success: false,
          error: 'Delete operation cancelled by user'
        }
      }

      const { globalFileOperations } = await import('../../background/file-operations')
      return await globalFileOperations.deleteFile(item.path || '')
    } catch (error) {
      return {
        success: false,
        error: `Failed to delete: ${error instanceof Error ? error.message : String(error)}`
      }
    }
  }

  /**
   * Handle duplicate action
   */
  private async handleDuplicate(item: FileSystemItem): Promise<FileOperationResult> {
    try {
      const { globalFileOperations } = await import('../../background/file-operations')
      const duplicateName = this.generateDuplicateName(item.name)
      const duplicatePath = this.getDuplicatePath(item.path || '', duplicateName)
      
      return await globalFileOperations.copyFile(item.path || '', duplicatePath)
    } catch (error) {
      return {
        success: false,
        error: `Failed to duplicate: ${error instanceof Error ? error.message : String(error)}`
      }
    }
  }

  /**
   * Handle copy path action
   */
  private async handleCopyPath(item: FileSystemItem): Promise<FileOperationResult> {
    try {
      await this.clipboardManager.copyPath(item.path || '')
      return {
        success: true,
        data: { action: 'copy-path', path: item.path }
      }
    } catch (error) {
      return {
        success: false,
        error: `Failed to copy path: ${error instanceof Error ? error.message : String(error)}`
      }
    }
  }

  /**
   * Handle copy relative path action
   */
  private async handleCopyRelativePath(item: FileSystemItem): Promise<FileOperationResult> {
    try {
      // Get workspace root path
      const workspaceRoot = this.getWorkspaceRoot()
      await this.clipboardManager.copyRelativePath(item.path || '', workspaceRoot)
      return {
        success: true,
        data: { action: 'copy-relative-path', path: item.path }
      }
    } catch (error) {
      return {
        success: false,
        error: `Failed to copy relative path: ${error instanceof Error ? error.message : String(error)}`
      }
    }
  }

  /**
   * Handle reveal in explorer action
   */
  private async handleReveal(item: FileSystemItem): Promise<FileOperationResult> {
    try {
      // Use Electron's shell.showItemInFolder if available
      if (typeof window !== 'undefined' && (window as any).electronAPI) {
        await (window as any).electronAPI.showItemInFolder(item.path)
        return { success: true }
      }
      
      // Fallback for web environments
      return {
        success: false,
        error: 'Reveal in explorer not available in web environment'
      }
    } catch (error) {
      return {
        success: false,
        error: `Failed to reveal in explorer: ${error instanceof Error ? error.message : String(error)}`
      }
    }
  }

  /**
   * Handle properties action
   */
  private async handleProperties(item: FileSystemItem): Promise<FileOperationResult> {
    // This would show a properties dialog
    return {
      success: true,
      data: { action: 'properties', item: item.name, triggerUI: true }
    }
  }

  /**
   * Handle new file action
   */
  private async handleNewFile(item: FileSystemItem): Promise<FileOperationResult> {
    return {
      success: true,
      data: { action: 'new-file', parentPath: item.path, triggerUI: true }
    }
  }

  /**
   * Handle new folder action
   */
  private async handleNewFolder(item: FileSystemItem): Promise<FileOperationResult> {
    return {
      success: true,
      data: { action: 'new-folder', parentPath: item.path, triggerUI: true }
    }
  }

  /**
   * Handle open terminal action
   */
  private async handleOpenTerminal(item: FileSystemItem): Promise<FileOperationResult> {
    try {
      if (typeof window !== 'undefined' && (window as any).electronAPI) {
        await (window as any).electronAPI.openTerminal(item.path)
        return { success: true }
      }
      
      return {
        success: false,
        error: 'Terminal not available in web environment'
      }
    } catch (error) {
      return {
        success: false,
        error: `Failed to open terminal: ${error instanceof Error ? error.message : String(error)}`
      }
    }
  }

  /**
   * Handle compress action
   */
  private async handleCompress(item: FileSystemItem): Promise<FileOperationResult> {
    return {
      success: false,
      error: 'Compression not yet implemented'
    }
  }

  /**
   * Handle extract action
   */
  private async handleExtract(item: FileSystemItem): Promise<FileOperationResult> {
    return {
      success: false,
      error: 'Extraction not yet implemented'
    }
  }

  /**
   * Handle git add action
   */
  private async handleGitAdd(item: FileSystemItem): Promise<FileOperationResult> {
    return {
      success: false,
      error: 'Git operations not yet implemented'
    }
  }

  /**
   * Handle git commit action
   */
  private async handleGitCommit(item: FileSystemItem): Promise<FileOperationResult> {
    return {
      success: false,
      error: 'Git operations not yet implemented'
    }
  }

  /**
   * Utility methods
   */
  private isArchiveFile(filename: string): boolean {
    const archiveExtensions = ['.zip', '.rar', '.7z', '.tar', '.gz', '.bz2']
    return archiveExtensions.some(ext => filename.toLowerCase().endsWith(ext))
  }

  private isInGitRepository(path: string): boolean {
    // This would check if the path is within a git repository
    // For now, return false as placeholder
    return false
  }

  private async showDeleteConfirmation(item: FileSystemItem): Promise<boolean> {
    // This would show a proper confirmation dialog
    // For now, use browser confirm as fallback
    return confirm(`Are you sure you want to delete "${item.name}"?`)
  }

  private generateDuplicateName(originalName: string): string {
    const lastDot = originalName.lastIndexOf('.')
    if (lastDot === -1) {
      return `${originalName} copy`
    }
    
    const nameWithoutExt = originalName.substring(0, lastDot)
    const extension = originalName.substring(lastDot)
    return `${nameWithoutExt} copy${extension}`
  }

  private getDuplicatePath(originalPath: string, duplicateName: string): string {
    const lastSlash = Math.max(originalPath.lastIndexOf('/'), originalPath.lastIndexOf('\\'))
    if (lastSlash === -1) {
      return duplicateName
    }
    
    const directory = originalPath.substring(0, lastSlash + 1)
    return directory + duplicateName
  }

  private getWorkspaceRoot(): string {
    // This would get the actual workspace root
    // For now, return a placeholder
    return process.cwd() || '/'
  }
}
