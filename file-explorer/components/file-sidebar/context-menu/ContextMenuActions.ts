/**
 * Context Menu Actions Handler
 * Implements all context menu actions with proper file operations integration
 */

import { ContextMenuAction, ContextMenuActionHandler, FileOperationResult } from './types'
import { FileSystemItem } from '../types'
import { ClipboardManager } from './ClipboardManager'

export class ContextMenuActionHandler implements ContextMenuActionHandler {
  private clipboardManager: ClipboardManager

  constructor(clipboardManager: ClipboardManager) {
    this.clipboardManager = clipboardManager
  }

  /**
   * Check if action can be executed for the given item
   */
  canExecute(action: ContextMenuAction, item: FileSystemItem): boolean {
    switch (action) {
      case 'open':
      case 'copy':
      case 'cut':
      case 'rename':
      case 'delete':
      case 'copy-path':
      case 'reveal':
        return true

      case 'paste':
        return item.type === 'folder' && this.clipboardManager.canPaste()

      case 'new-file':
      case 'new-folder':
        return item.type === 'folder'

      default:
        return false
    }
  }

  /**
   * Validate that file/folder exists before performing operations
   */
  private async validateItemExists(item: FileSystemItem): Promise<FileOperationResult | null> {
    if (!item.path) {
      return {
        success: false,
        error: 'Item has no path specified'
      }
    }

    // For file operations that require the item to exist, validate existence
    if (typeof window !== 'undefined' && window.electronAPI) {
      try {
        if (item.type === 'folder') {
          // Try to read directory to check if it exists
          const result = await window.electronAPI.readDirectory(item.path)
          if (!result.success) {
            return {
              success: false,
              error: `Folder does not exist: ${item.path}`
            }
          }
        } else {
          // Try to read file to check if it exists
          const result = await window.electronAPI.readFile(item.path)
          if (!result.success) {
            return {
              success: false,
              error: `File does not exist: ${item.path}`
            }
          }
        }
      } catch (error) {
        return {
          success: false,
          error: `Failed to validate item existence: ${error instanceof Error ? error.message : String(error)}`
        }
      }
    }

    return null // No error, item exists
  }

  /**
   * Execute the specified action
   */
  async execute(action: ContextMenuAction, item: FileSystemItem): Promise<FileOperationResult> {
    if (!this.canExecute(action, item)) {
      return {
        success: false,
        error: `Action '${action}' cannot be executed for this item`
      }
    }

    // Validate item exists for operations that require it
    const actionsRequiringExistence = ['open', 'copy', 'cut', 'rename', 'delete', 'copy-path', 'reveal']
    if (actionsRequiringExistence.includes(action)) {
      const validationError = await this.validateItemExists(item)
      if (validationError) {
        return validationError
      }
    }

    try {
      switch (action) {
        case 'open':
          return await this.handleOpen(item)
        case 'copy':
          return await this.handleCopy(item)
        case 'cut':
          return await this.handleCut(item)
        case 'paste':
          return await this.handlePaste(item)
        case 'rename':
          return await this.handleRename(item)
        case 'delete':
          return await this.handleDelete(item)
        case 'copy-path':
          return await this.handleCopyPath(item)
        case 'reveal':
          return await this.handleReveal(item)
        case 'new-file':
          return await this.handleNewFile(item)
        case 'new-folder':
          return await this.handleNewFolder(item)
        default:
          return {
            success: false,
            error: `Unknown action: ${action}`
          }
      }
    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : String(error)
      }
    }
  }

  /**
   * Handle open action
   */
  private async handleOpen(item: FileSystemItem): Promise<FileOperationResult> {
    try {
      const filePath = item.path || ''

      // Try to open with Electron API if available
      if (typeof window !== 'undefined' && (window as any).electronAPI) {
        try {
          await (window as any).electronAPI.openFile(filePath)
          return { success: true, data: { action: 'open', path: filePath } }
        } catch (electronError) {
          console.warn('Electron open failed, trying fallback:', electronError)
        }
      }

      // Fallback: trigger file selection event for the file explorer
      // This will open the file in the editor
      if (item.type === 'file') {
        // Dispatch a custom event to notify the file explorer to select this file
        const event = new CustomEvent('file-explorer-select', {
          detail: { path: filePath, item }
        })
        window.dispatchEvent(event)

        this.showTemporaryNotification(`Opening ${item.name}`)
        return {
          success: true,
          data: { action: 'open', path: filePath, method: 'file-explorer-select' }
        }
      } else {
        return {
          success: false,
          error: 'Cannot open folders directly'
        }
      }
    } catch (error) {
      return {
        success: false,
        error: `Failed to open file: ${error instanceof Error ? error.message : String(error)}`
      }
    }
  }



  /**
   * Handle copy action
   */
  private async handleCopy(item: FileSystemItem): Promise<FileOperationResult> {
    try {
      await this.clipboardManager.copyItem(item)
      return {
        success: true,
        data: { action: 'copy', item: item.name }
      }
    } catch (error) {
      return {
        success: false,
        error: `Failed to copy: ${error instanceof Error ? error.message : String(error)}`
      }
    }
  }

  /**
   * Handle cut action
   */
  private async handleCut(item: FileSystemItem): Promise<FileOperationResult> {
    try {
      await this.clipboardManager.cutItem(item)
      return {
        success: true,
        data: { action: 'cut', item: item.name }
      }
    } catch (error) {
      return {
        success: false,
        error: `Failed to cut: ${error instanceof Error ? error.message : String(error)}`
      }
    }
  }

  /**
   * Handle paste action
   */
  private async handlePaste(item: FileSystemItem): Promise<FileOperationResult> {
    try {
      const targetPath = item.path || ''

      // Check if there are items in clipboard
      if (!this.clipboardManager.canPaste()) {
        return {
          success: false,
          error: 'No items in clipboard to paste'
        }
      }

      // Use electronAPI for real file operations
      if (typeof window !== 'undefined' && window.electronAPI) {
        const clipboardItems = this.clipboardManager.getClipboard()
        const operation = clipboardItems.length > 0 ? clipboardItems[0].operation : 'copy'

        let successCount = 0
        let errorCount = 0
        const errors: string[] = []

        for (const clipboardItem of clipboardItems) {
          try {
            const sourcePath = clipboardItem.path
            const fileName = clipboardItem.name
            const targetFilePath = targetPath ? `${targetPath}/${fileName}` : fileName

            if (operation === 'copy') {
              // Copy operation: read source and create new file
              if (clipboardItem.type === 'file') {
                const readResult = await window.electronAPI.readFile(sourcePath)
                if (readResult.success) {
                  const createResult = await window.electronAPI.createFile(targetFilePath, readResult.content || '')
                  if (createResult.success) {
                    successCount++
                  } else {
                    errorCount++
                    errors.push(`Failed to copy ${fileName}: ${createResult.error}`)
                  }
                } else {
                  errorCount++
                  errors.push(`Failed to read ${fileName}: ${readResult.error}`)
                }
              } else {
                // For folders, we'll show an error for now
                errorCount++
                errors.push(`Folder copying not yet supported: ${fileName}`)
              }
            } else if (operation === 'cut') {
              // Move operation: copy then delete
              if (clipboardItem.type === 'file') {
                const readResult = await window.electronAPI.readFile(sourcePath)
                if (readResult.success) {
                  const createResult = await window.electronAPI.createFile(targetFilePath, readResult.content || '')
                  if (createResult.success) {
                    const deleteResult = await window.electronAPI.deleteFile(sourcePath)
                    if (deleteResult.success) {
                      successCount++
                    } else {
                      // Clean up the copied file if delete failed
                      await window.electronAPI.deleteFile(targetFilePath)
                      errorCount++
                      errors.push(`Failed to move ${fileName}: could not delete source`)
                    }
                  } else {
                    errorCount++
                    errors.push(`Failed to move ${fileName}: ${createResult.error}`)
                  }
                } else {
                  errorCount++
                  errors.push(`Failed to read ${fileName}: ${readResult.error}`)
                }
              } else {
                // For folders, we'll show an error for now
                errorCount++
                errors.push(`Folder moving not yet supported: ${fileName}`)
              }
            }
          } catch (error) {
            errorCount++
            errors.push(`Error processing ${clipboardItem.name}: ${error instanceof Error ? error.message : String(error)}`)
          }
        }

        // Clear clipboard after cut operation
        if (operation === 'cut' && successCount > 0) {
          this.clipboardManager.clear()
        }

        // Trigger file explorer refresh
        await this.triggerFileExplorerRefresh()

        if (successCount > 0) {
          this.showTemporaryNotification(`${operation === 'copy' ? 'Copied' : 'Moved'} ${successCount} item(s)`)
        }

        if (errorCount > 0 && successCount === 0) {
          return {
            success: false,
            error: errors.join('; ')
          }
        } else {
          return {
            success: true,
            data: {
              action: 'paste',
              operation,
              successCount,
              errorCount,
              errors: errorCount > 0 ? errors : undefined
            }
          }
        }
      } else {
        return {
          success: false,
          error: 'File operations not available in web environment'
        }
      }
    } catch (error) {
      return {
        success: false,
        error: `Failed to paste: ${error instanceof Error ? error.message : String(error)}`
      }
    }
  }

  /**
   * Handle rename action
   */
  private async handleRename(item: FileSystemItem): Promise<FileOperationResult> {
    try {
      // Create a simple input dialog using DOM
      const newName = await this.showInputDialog(`Rename "${item.name}"`, item.name)
      if (!newName) {
        // User cancelled - return success with no action taken
        return {
          success: true,
          data: { action: 'rename-cancelled', message: 'Rename operation cancelled by user' }
        }
      }

      if (newName === item.name) {
        // No change made - return success with no action taken
        return {
          success: true,
          data: { action: 'rename-no-change', message: 'No change made to filename' }
        }
      }

      // Validate new name
      if (newName.includes('/') || newName.includes('\\') || newName.includes(':')) {
        return {
          success: false,
          error: 'Invalid filename: cannot contain / \\ or :'
        }
      }

      const currentPath = item.path || ''
      const parentPath = currentPath.substring(0, Math.max(currentPath.lastIndexOf('/'), currentPath.lastIndexOf('\\')))
      const separator = currentPath.includes('/') ? '/' : '\\'
      const newPath = parentPath ? `${parentPath}${separator}${newName}` : newName

      // Use electronAPI for real file operations
      if (typeof window !== 'undefined' && window.electronAPI) {
        if (item.type === 'file') {
          // For files: read content, create new file, delete old file
          const readResult = await window.electronAPI.readFile(currentPath)
          if (!readResult.success) {
            return {
              success: false,
              error: `Failed to read file for rename: ${readResult.error}`
            }
          }

          const createResult = await window.electronAPI.createFile(newPath, readResult.content || '')
          if (!createResult.success) {
            return {
              success: false,
              error: `Failed to create renamed file: ${createResult.error}`
            }
          }

          const deleteResult = await window.electronAPI.deleteFile(currentPath)
          if (!deleteResult.success) {
            // Try to clean up the new file if delete failed
            await window.electronAPI.deleteFile(newPath)
            return {
              success: false,
              error: `Failed to delete original file: ${deleteResult.error}`
            }
          }
        } else {
          // For folders, we need to implement folder renaming using available APIs
          // This involves creating a new folder and moving all contents
          const result = await this.renameFolderRecursively(currentPath, newPath)
          if (!result.success) {
            return result
          }
        }

        // Trigger file explorer refresh
        await this.triggerFileExplorerRefresh()
        this.showTemporaryNotification(`Renamed to: ${newName}`)

        return {
          success: true,
          data: { action: 'rename', oldPath: currentPath, newPath: newPath, newName: newName }
        }
      } else {
        return {
          success: false,
          error: 'File operations not available in web environment'
        }
      }
    } catch (error) {
      return {
        success: false,
        error: `Failed to rename: ${error instanceof Error ? error.message : String(error)}`
      }
    }
  }

  /**
   * Handle delete action
   */
  private async handleDelete(item: FileSystemItem): Promise<FileOperationResult> {
    try {
      // Show confirmation dialog first
      const confirmed = await this.showDeleteConfirmation(item)
      if (!confirmed) {
        return {
          success: true,
          data: { action: 'delete-cancelled', message: 'Delete operation cancelled by user' }
        }
      }

      const filePath = item.path || ''

      // Use electronAPI for real file operations
      if (typeof window !== 'undefined' && window.electronAPI) {
        let result

        if (item.type === 'folder') {
          // For folders, we need to check if electronAPI has a deleteDirectory method
          // If not, we'll show an error message
          if (window.electronAPI.deleteDirectory) {
            result = await window.electronAPI.deleteDirectory(filePath)
          } else {
            return {
              success: false,
              error: 'Folder deletion not supported. Please delete manually in file explorer.'
            }
          }
        } else {
          result = await window.electronAPI.deleteFile(filePath)
        }

        if (result.success) {
          // Trigger file explorer refresh
          await this.triggerFileExplorerRefresh()
          this.showTemporaryNotification(`Deleted: ${item.name}`)

          return {
            success: true,
            data: { action: 'delete', path: filePath, name: item.name, type: item.type }
          }
        } else {
          return {
            success: false,
            error: result.error || 'Failed to delete item'
          }
        }
      } else {
        return {
          success: false,
          error: 'File operations not available in web environment'
        }
      }
    } catch (error) {
      return {
        success: false,
        error: `Failed to delete: ${error instanceof Error ? error.message : String(error)}`
      }
    }
  }



  /**
   * Handle copy path action
   */
  private async handleCopyPath(item: FileSystemItem): Promise<FileOperationResult> {
    try {
      const path = item.path || ''
      if (!path) {
        return {
          success: false,
          error: 'No path available for this item'
        }
      }

      await this.clipboardManager.copyPath(path)

      // Show temporary notification
      this.showTemporaryNotification(`Path copied: ${path}`)

      return {
        success: true,
        data: { action: 'copy-path', path: path, message: 'Path copied to clipboard' }
      }
    } catch (error) {
      return {
        success: false,
        error: `Failed to copy path: ${error instanceof Error ? error.message : String(error)}`
      }
    }
  }



  /**
   * Handle reveal in explorer action
   */
  private async handleReveal(item: FileSystemItem): Promise<FileOperationResult> {
    try {
      const filePath = item.path || ''

      // Try Electron API first
      if (typeof window !== 'undefined' && (window as any).electronAPI && (window as any).electronAPI.showItemInFolder) {
        try {
          await (window as any).electronAPI.showItemInFolder(filePath)
          return { success: true, data: { action: 'reveal', path: filePath } }
        } catch (electronError) {
          console.warn('Electron reveal failed, trying fallback:', electronError)
        }
      }

      // Fallback: Copy path and show notification
      await this.clipboardManager.copyPath(filePath)
      this.showTemporaryNotification(`Path copied to clipboard: ${filePath}`)

      return {
        success: true,
        data: {
          action: 'reveal-fallback',
          path: filePath,
          message: 'Path copied to clipboard (reveal not available in web environment)'
        }
      }
    } catch (error) {
      return {
        success: false,
        error: `Failed to reveal in explorer: ${error instanceof Error ? error.message : String(error)}`
      }
    }
  }



  /**
   * Handle new file action
   */
  private async handleNewFile(item: FileSystemItem): Promise<FileOperationResult> {
    try {
      const fileName = await this.showInputDialog('Enter file name:', 'new-file.txt')
      if (!fileName) {
        return {
          success: true,
          data: { action: 'new-file-cancelled', message: 'File creation cancelled by user' }
        }
      }

      // Validate filename
      if (fileName.includes('/') || fileName.includes('\\') || fileName.includes(':')) {
        return {
          success: false,
          error: 'Invalid filename: cannot contain / \\ or :'
        }
      }

      const parentPath = item.path || ''
      const filePath = parentPath ? `${parentPath}/${fileName}` : fileName

      // Use electronAPI directly for real file operations
      if (typeof window !== 'undefined' && window.electronAPI) {
        const result = await window.electronAPI.createFile(filePath, '')

        if (result.success) {
          // Trigger file explorer refresh
          await this.triggerFileExplorerRefresh()
          this.showTemporaryNotification(`File created: ${fileName}`)

          return {
            success: true,
            data: { action: 'new-file', path: filePath, name: fileName }
          }
        } else {
          return {
            success: false,
            error: result.error || 'Failed to create file'
          }
        }
      } else {
        return {
          success: false,
          error: 'File operations not available in web environment'
        }
      }
    } catch (error) {
      return {
        success: false,
        error: `Failed to create file: ${error instanceof Error ? error.message : String(error)}`
      }
    }
  }

  /**
   * Handle new folder action
   */
  private async handleNewFolder(item: FileSystemItem): Promise<FileOperationResult> {
    try {
      const folderName = await this.showInputDialog('Enter folder name:', 'new-folder')
      if (!folderName) {
        return {
          success: true,
          data: { action: 'new-folder-cancelled', message: 'Folder creation cancelled by user' }
        }
      }

      // Validate folder name
      if (folderName.includes('/') || folderName.includes('\\') || folderName.includes(':')) {
        return {
          success: false,
          error: 'Invalid folder name: cannot contain / \\ or :'
        }
      }

      const parentPath = item.path || ''
      const folderPath = parentPath ? `${parentPath}/${folderName}` : folderName

      // Use electronAPI directly for real folder operations
      if (typeof window !== 'undefined' && window.electronAPI) {
        const result = await window.electronAPI.ensureDirectory(folderPath)

        if (result.success) {
          // Trigger file explorer refresh
          await this.triggerFileExplorerRefresh()
          this.showTemporaryNotification(`Folder created: ${folderName}`)

          return {
            success: true,
            data: { action: 'new-folder', path: folderPath, name: folderName }
          }
        } else {
          return {
            success: false,
            error: result.error || 'Failed to create folder'
          }
        }
      } else {
        return {
          success: false,
          error: 'File operations not available in web environment'
        }
      }
    } catch (error) {
      return {
        success: false,
        error: `Failed to create folder: ${error instanceof Error ? error.message : String(error)}`
      }
    }
  }



  /**
   * Rename folder recursively by creating new folder and moving contents
   */
  private async renameFolderRecursively(oldPath: string, newPath: string): Promise<FileOperationResult> {
    try {
      // First, create the new directory
      const createResult = await window.electronAPI.ensureDirectory(newPath)
      if (!createResult.success) {
        return {
          success: false,
          error: `Failed to create new folder: ${createResult.error}`
        }
      }

      // Read the contents of the old directory
      const readResult = await window.electronAPI.readDirectory(oldPath)
      if (!readResult.success) {
        return {
          success: false,
          error: `Failed to read folder contents: ${readResult.error}`
        }
      }

      // Move each item from old to new location
      if (readResult.items && readResult.items.length > 0) {
        for (const item of readResult.items) {
          const oldItemPath = `${oldPath}/${item.name}`
          const newItemPath = `${newPath}/${item.name}`

          if (item.type === 'folder') {
            // Recursively rename subfolder
            const subResult = await this.renameFolderRecursively(oldItemPath, newItemPath)
            if (!subResult.success) {
              return subResult
            }
          } else {
            // Move file: read content, create new file, delete old file
            const fileReadResult = await window.electronAPI.readFile(oldItemPath)
            if (!fileReadResult.success) {
              return {
                success: false,
                error: `Failed to read file ${item.name}: ${fileReadResult.error}`
              }
            }

            const fileCreateResult = await window.electronAPI.createFile(newItemPath, fileReadResult.content || '')
            if (!fileCreateResult.success) {
              return {
                success: false,
                error: `Failed to create file ${item.name}: ${fileCreateResult.error}`
              }
            }

            const fileDeleteResult = await window.electronAPI.deleteFile(oldItemPath)
            if (!fileDeleteResult.success) {
              return {
                success: false,
                error: `Failed to delete original file ${item.name}: ${fileDeleteResult.error}`
              }
            }
          }
        }
      }

      // Finally, delete the old empty directory
      // Note: We'll use deleteFile for now since deleteDirectory might not be available
      // This might fail if the directory is not empty, but we've moved all contents
      try {
        if (window.electronAPI.deleteDirectory) {
          await window.electronAPI.deleteDirectory(oldPath)
        } else {
          // Try using deleteFile - some implementations might handle directories
          await window.electronAPI.deleteFile(oldPath)
        }
      } catch (error) {
        // If we can't delete the old directory, that's not critical since we've moved all contents
        console.warn(`Could not delete old directory ${oldPath}:`, error)
      }

      return { success: true }
    } catch (error) {
      return {
        success: false,
        error: `Failed to rename folder: ${error instanceof Error ? error.message : String(error)}`
      }
    }
  }

  /**
   * Utility methods
   */
  private async showDeleteConfirmation(item: FileSystemItem): Promise<boolean> {
    return this.showConfirmDialog(
      `Delete "${item.name}"?`,
      `Are you sure you want to delete "${item.name}"? This action cannot be undone.`,
      'Delete',
      'Cancel'
    )
  }

  private async showInputDialog(title: string, defaultValue: string = ''): Promise<string | null> {
    return new Promise((resolve) => {
      // Create modal overlay
      const overlay = document.createElement('div')
      overlay.style.cssText = `
        position: fixed;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background: rgba(0, 0, 0, 0.5);
        display: flex;
        align-items: center;
        justify-content: center;
        z-index: 10000;
      `

      // Create dialog
      const dialog = document.createElement('div')
      dialog.style.cssText = `
        background: white;
        border-radius: 8px;
        padding: 24px;
        min-width: 300px;
        box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1);
      `

      dialog.innerHTML = `
        <h3 style="margin: 0 0 16px 0; font-size: 18px; font-weight: 600; color: #1f2937;">${title}</h3>
        <input type="text" value="${defaultValue}" style="
          width: 100%;
          padding: 8px 12px;
          border: 1px solid #d1d5db;
          border-radius: 6px;
          font-size: 14px;
          margin-bottom: 16px;
          box-sizing: border-box;
        " />
        <div style="display: flex; gap: 8px; justify-content: flex-end;">
          <button class="cancel-btn" style="
            padding: 8px 16px;
            border: 1px solid #d1d5db;
            background: white;
            border-radius: 6px;
            cursor: pointer;
            font-size: 14px;
          ">Cancel</button>
          <button class="ok-btn" style="
            padding: 8px 16px;
            border: none;
            background: #3b82f6;
            color: white;
            border-radius: 6px;
            cursor: pointer;
            font-size: 14px;
          ">OK</button>
        </div>
      `

      const input = dialog.querySelector('input') as HTMLInputElement
      const cancelBtn = dialog.querySelector('.cancel-btn') as HTMLButtonElement
      const okBtn = dialog.querySelector('.ok-btn') as HTMLButtonElement

      const cleanup = () => {
        if (overlay.parentNode) {
          overlay.parentNode.removeChild(overlay)
        }
      }

      cancelBtn.onclick = () => {
        cleanup()
        resolve(null)
      }

      okBtn.onclick = () => {
        const value = input.value.trim()
        cleanup()
        resolve(value || null)
      }

      input.onkeydown = (e) => {
        if (e.key === 'Enter') {
          okBtn.click()
        } else if (e.key === 'Escape') {
          cancelBtn.click()
        }
      }

      overlay.onclick = (e) => {
        if (e.target === overlay) {
          cancelBtn.click()
        }
      }

      overlay.appendChild(dialog)
      document.body.appendChild(overlay)
      input.focus()
      input.select()
    })
  }

  private async showConfirmDialog(title: string, message: string, confirmText: string = 'OK', cancelText: string = 'Cancel'): Promise<boolean> {
    return new Promise((resolve) => {
      // Create modal overlay
      const overlay = document.createElement('div')
      overlay.style.cssText = `
        position: fixed;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background: rgba(0, 0, 0, 0.5);
        display: flex;
        align-items: center;
        justify-content: center;
        z-index: 10000;
      `

      // Create dialog
      const dialog = document.createElement('div')
      dialog.style.cssText = `
        background: white;
        border-radius: 8px;
        padding: 24px;
        min-width: 300px;
        max-width: 400px;
        box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1);
      `

      dialog.innerHTML = `
        <h3 style="margin: 0 0 12px 0; font-size: 18px; font-weight: 600; color: #1f2937;">${title}</h3>
        <p style="margin: 0 0 20px 0; color: #6b7280; line-height: 1.5;">${message}</p>
        <div style="display: flex; gap: 8px; justify-content: flex-end;">
          <button class="cancel-btn" style="
            padding: 8px 16px;
            border: 1px solid #d1d5db;
            background: white;
            border-radius: 6px;
            cursor: pointer;
            font-size: 14px;
          ">${cancelText}</button>
          <button class="confirm-btn" style="
            padding: 8px 16px;
            border: none;
            background: #ef4444;
            color: white;
            border-radius: 6px;
            cursor: pointer;
            font-size: 14px;
          ">${confirmText}</button>
        </div>
      `

      const cancelBtn = dialog.querySelector('.cancel-btn') as HTMLButtonElement
      const confirmBtn = dialog.querySelector('.confirm-btn') as HTMLButtonElement

      const cleanup = () => {
        if (overlay.parentNode) {
          overlay.parentNode.removeChild(overlay)
        }
      }

      cancelBtn.onclick = () => {
        cleanup()
        resolve(false)
      }

      confirmBtn.onclick = () => {
        cleanup()
        resolve(true)
      }

      overlay.onclick = (e) => {
        if (e.target === overlay) {
          cancelBtn.click()
        }
      }

      overlay.appendChild(dialog)
      document.body.appendChild(overlay)
      confirmBtn.focus()
    })
  }

  private async triggerFileExplorerRefresh(): Promise<void> {
    try {
      // Trigger refresh via global window function if available
      if (typeof window !== 'undefined' && (window as any).refreshFileExplorer) {
        console.log('🔄 Calling window.refreshFileExplorer()')
        await (window as any).refreshFileExplorer()
        console.log('✅ File explorer refresh completed')
      }

      // Also trigger file system monitor refresh
      try {
        const { globalFileSystemMonitor } = await import('../../background/file-system-monitor')
        globalFileSystemMonitor.triggerRefresh()
        console.log('✅ File system monitoring refresh triggered')
      } catch (error) {
        console.warn('Failed to trigger file system monitor refresh:', error)
      }

      // Dispatch a custom event for components to listen to
      const event = new CustomEvent('file-system-changed', {
        detail: { type: 'refresh', timestamp: Date.now() }
      })
      window.dispatchEvent(event)

      console.log('🔄 Triggered file explorer refresh')
    } catch (error) {
      console.warn('Failed to trigger file explorer refresh:', error)
    }
  }

  private showTemporaryNotification(message: string): void {
    // Simple temporary notification - could be enhanced with a proper toast system
    console.log(`✅ ${message}`)

    // Try to show a temporary visual notification if possible
    if (typeof window !== 'undefined') {
      const notification = document.createElement('div')
      notification.textContent = message
      notification.style.cssText = `
        position: fixed;
        top: 20px;
        right: 20px;
        background: #10b981;
        color: white;
        padding: 8px 16px;
        border-radius: 6px;
        font-size: 14px;
        z-index: 10000;
        box-shadow: 0 4px 12px rgba(0,0,0,0.15);
      `

      document.body.appendChild(notification)

      setTimeout(() => {
        if (notification.parentNode) {
          notification.parentNode.removeChild(notification)
        }
      }, 2000)
    }
  }
}
