/**
 * Context Menu System - Main Export
 * Centralized exports for the context menu system
 */

// Main components
export { 
  FileContextMenu, 
  FileContextMenuWrapper 
} from './FileContextMenu'

export { 
  FolderContextMenu, 
  FolderContextMenuWrapper 
} from './FolderContextMenu'

// Action handler
export { ContextMenuActionHandler } from './ContextMenuActions'

// Clipboard manager
export { 
  ClipboardManager, 
  getClipboardManager, 
  useClipboardManager 
} from './ClipboardManager'

// Types
export type {
  ContextMenuAction,
  ContextMenuItem,
  ContextMenuPosition,
  ContextMenuProps,
  ClipboardItem,
  FileOperationResult,
  ContextMenuActionHandler as IContextMenuActionHandler,
  ContextMenuConfig,
  ContextMenuState
} from './types'

// Predefined menu configurations
export {
  COMMON_FILE_ACTIONS,
  EDIT_ACTIONS,
  PATH_ACTIONS,
  FOLDER_CREATION_ACTIONS,
  ADVANCED_ACTIONS
} from './types'
