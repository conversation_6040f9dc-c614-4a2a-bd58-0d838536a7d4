/**
 * Clipboard Manager
 * Handles copy/cut/paste operations for files and folders
 */

import { ClipboardItem, FileOperationResult } from './types'
import { FileSystemItem } from '../types'

export class ClipboardManager {
  private clipboard: ClipboardItem[] = []
  private subscribers: Set<(items: ClipboardItem[]) => void> = new Set()

  /**
   * Copy file or folder to clipboard
   */
  async copyItem(item: FileSystemItem): Promise<void> {
    const clipboardItem: ClipboardItem = {
      type: item.type === 'folder' ? 'folder' : 'file',
      path: item.path || '',
      name: item.name,
      operation: 'copy',
      timestamp: Date.now()
    }

    this.clipboard = [clipboardItem]
    this.notifySubscribers()

    // Also copy path to system clipboard
    if (navigator.clipboard && item.path) {
      try {
        await navigator.clipboard.writeText(item.path)
      } catch (error) {
        console.warn('Failed to copy path to system clipboard:', error)
      }
    }
  }

  /**
   * Cut file or folder to clipboard
   */
  async cutItem(item: FileSystemItem): Promise<void> {
    const clipboardItem: ClipboardItem = {
      type: item.type === 'folder' ? 'folder' : 'file',
      path: item.path || '',
      name: item.name,
      operation: 'cut',
      timestamp: Date.now()
    }

    this.clipboard = [clipboardItem]
    this.notifySubscribers()
  }

  /**
   * Copy multiple items to clipboard
   */
  async copyItems(items: FileSystemItem[]): Promise<void> {
    this.clipboard = items.map(item => ({
      type: item.type === 'folder' ? 'folder' : 'file',
      path: item.path || '',
      name: item.name,
      operation: 'copy',
      timestamp: Date.now()
    }))

    this.notifySubscribers()

    // Copy paths to system clipboard (newline separated)
    if (navigator.clipboard) {
      try {
        const paths = items.map(item => item.path || '').filter(Boolean)
        await navigator.clipboard.writeText(paths.join('\n'))
      } catch (error) {
        console.warn('Failed to copy paths to system clipboard:', error)
      }
    }
  }

  /**
   * Cut multiple items to clipboard
   */
  async cutItems(items: FileSystemItem[]): Promise<void> {
    this.clipboard = items.map(item => ({
      type: item.type === 'folder' ? 'folder' : 'file',
      path: item.path || '',
      name: item.name,
      operation: 'cut',
      timestamp: Date.now()
    }))

    this.notifySubscribers()
  }

  /**
   * Paste items from clipboard to target location
   */
  async paste(targetPath: string): Promise<FileOperationResult> {
    if (this.clipboard.length === 0) {
      return {
        success: false,
        error: 'Clipboard is empty'
      }
    }

    try {
      const results: FileOperationResult[] = []

      for (const item of this.clipboard) {
        if (item.operation === 'copy') {
          const result = await this.copyItemToTarget(item, targetPath)
          results.push(result)
        } else if (item.operation === 'cut') {
          const result = await this.moveItemToTarget(item, targetPath)
          results.push(result)
        }
      }

      // Clear clipboard after cut operations
      const hasCutOperations = this.clipboard.some(item => item.operation === 'cut')
      if (hasCutOperations) {
        this.clear()
      }

      // Check if all operations succeeded
      const allSucceeded = results.every(result => result.success)
      const errors = results.filter(result => !result.success).map(result => result.error)

      return {
        success: allSucceeded,
        error: errors.length > 0 ? errors.join('; ') : undefined,
        data: { operationCount: results.length, results }
      }

    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : String(error)
      }
    }
  }

  /**
   * Copy path to system clipboard
   */
  async copyPath(path: string): Promise<void> {
    if (navigator.clipboard) {
      try {
        await navigator.clipboard.writeText(path)
      } catch (error) {
        console.warn('Failed to copy path to system clipboard:', error)
        // Fallback: create temporary textarea
        this.fallbackCopyToClipboard(path)
      }
    } else {
      this.fallbackCopyToClipboard(path)
    }
  }

  /**
   * Copy relative path to system clipboard
   */
  async copyRelativePath(fullPath: string, basePath: string): Promise<void> {
    const relativePath = this.getRelativePath(fullPath, basePath)
    await this.copyPath(relativePath)
  }

  /**
   * Get clipboard contents
   */
  getClipboard(): ClipboardItem[] {
    return [...this.clipboard]
  }

  /**
   * Check if clipboard has items
   */
  hasItems(): boolean {
    return this.clipboard.length > 0
  }

  /**
   * Check if clipboard has items that can be pasted
   */
  canPaste(): boolean {
    return this.clipboard.length > 0 && 
           this.clipboard.every(item => Date.now() - item.timestamp < 24 * 60 * 60 * 1000) // 24 hours
  }

  /**
   * Clear clipboard
   */
  clear(): void {
    this.clipboard = []
    this.notifySubscribers()
  }

  /**
   * Subscribe to clipboard changes
   */
  subscribe(callback: (items: ClipboardItem[]) => void): () => void {
    this.subscribers.add(callback)
    return () => this.subscribers.delete(callback)
  }

  /**
   * Copy item to target location
   */
  private async copyItemToTarget(item: ClipboardItem, targetPath: string): Promise<FileOperationResult> {
    try {
      // Use FileOperationsManager for actual file operations
      const { globalFileOperations } = await import('../../background/file-operations')
      
      const targetFilePath = this.joinPaths(targetPath, item.name)
      
      const agentId = 'clipboard-manager'
      const options = { validatePath: true, createDirectories: true }

      if (item.type === 'file') {
        return await globalFileOperations.copyFile(item.path, targetFilePath, options, agentId)
      } else {
        // For folders, we need to copy recursively
        return await this.copyFolderRecursively(item.path, targetFilePath)
      }
    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : String(error)
      }
    }
  }

  /**
   * Move item to target location
   */
  private async moveItemToTarget(item: ClipboardItem, targetPath: string): Promise<FileOperationResult> {
    try {
      const { globalFileOperations } = await import('../../background/file-operations')
      
      const targetFilePath = this.joinPaths(targetPath, item.name)

      const agentId = 'clipboard-manager'
      const options = { validatePath: true, createDirectories: true }
      return await globalFileOperations.moveFile(item.path, targetFilePath, options, agentId)
    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : String(error)
      }
    }
  }

  /**
   * Copy folder recursively
   */
  private async copyFolderRecursively(sourcePath: string, targetPath: string): Promise<FileOperationResult> {
    try {
      // This would need to be implemented with proper recursive folder copying
      // For now, return a placeholder implementation
      return {
        success: false,
        error: 'Recursive folder copying not yet implemented'
      }
    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : String(error)
      }
    }
  }

  /**
   * Get relative path
   */
  private getRelativePath(fullPath: string, basePath: string): string {
    if (fullPath.startsWith(basePath)) {
      return fullPath.substring(basePath.length).replace(/^[/\\]/, '')
    }
    return fullPath
  }

  /**
   * Join paths safely
   */
  private joinPaths(basePath: string, fileName: string): string {
    const separator = basePath.includes('/') ? '/' : '\\'
    return basePath.endsWith(separator) ? basePath + fileName : basePath + separator + fileName
  }

  /**
   * Fallback clipboard copy for older browsers
   */
  private fallbackCopyToClipboard(text: string): void {
    const textarea = document.createElement('textarea')
    textarea.value = text
    textarea.style.position = 'fixed'
    textarea.style.opacity = '0'
    document.body.appendChild(textarea)
    textarea.select()
    
    try {
      document.execCommand('copy')
    } catch (error) {
      console.warn('Fallback clipboard copy failed:', error)
    } finally {
      document.body.removeChild(textarea)
    }
  }

  /**
   * Notify subscribers of clipboard changes
   */
  private notifySubscribers(): void {
    this.subscribers.forEach(callback => {
      try {
        callback([...this.clipboard])
      } catch (error) {
        console.error('Error notifying clipboard subscriber:', error)
      }
    })
  }
}

/**
 * Global clipboard manager instance
 */
let globalClipboardManager: ClipboardManager | null = null

/**
 * Get or create global clipboard manager
 */
export function getClipboardManager(): ClipboardManager {
  if (!globalClipboardManager) {
    globalClipboardManager = new ClipboardManager()
  }
  return globalClipboardManager
}

/**
 * React hook for using clipboard manager
 */
export function useClipboardManager(): ClipboardManager {
  return getClipboardManager()
}
