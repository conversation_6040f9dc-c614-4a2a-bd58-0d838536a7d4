/**
 * Folder Context Menu Component
 * Provides context menu for folder items with folder-specific actions
 */

import React, { useMemo, useCallback } from 'react'
import {
  FileText,
  FolderPlus,
  Copy,
  Scissors,
  Clipboard,
  Edit,
  Trash,
  Link,
  FolderOpen,
  Settings,
  Terminal,
  Archive,
  GitBranch,
  Search,
  RefreshCw
} from 'lucide-react'
import { ContextMenuProps, ContextMenuItem as MenuItemType, ContextMenuAction } from './types'
import { FileSystemItem } from '../types'
import { useClipboardManager } from './ClipboardManager'

export const FolderContextMenu: React.FC<ContextMenuProps> = ({
  item,
  position,
  onAction,
  onClose,
  visible
}) => {
  const clipboardManager = useClipboardManager()
  const canPaste = clipboardManager.canPaste()

  // Generate menu items for folder context
  const menuItems = useMemo((): MenuItemType[] => {
    const items: MenuItemType[] = []

    // Creation actions
    items.push(
      {
        id: 'new-file',
        label: 'New File',
        icon: FileText
      },
      {
        id: 'new-folder',
        label: 'New Folder',
        icon: FolderPlus
      }
    )

    // Separator
    items.push({ id: 'separator-1' as ContextMenuAction, label: '', separator: true })

    // Edit actions
    items.push(
      {
        id: 'copy',
        label: 'Copy',
        icon: Copy,
        shortcut: 'Ctrl+C'
      },
      {
        id: 'cut',
        label: 'Cut',
        icon: Scissors,
        shortcut: 'Ctrl+X'
      }
    )

    // Paste action (only if clipboard has items)
    if (canPaste) {
      items.push({
        id: 'paste',
        label: 'Paste',
        icon: Clipboard,
        shortcut: 'Ctrl+V'
      })
    }

    items.push(
      {
        id: 'rename',
        label: 'Rename',
        icon: Edit,
        shortcut: 'F2'
      },
      {
        id: 'delete',
        label: 'Delete',
        icon: Trash,
        shortcut: 'Delete',
        dangerous: true
      }
    )

    // Separator
    items.push({ id: 'separator-2' as ContextMenuAction, label: '', separator: true })

    // Path actions
    items.push(
      {
        id: 'copy-path',
        label: 'Copy Path',
        icon: Link
      },
      {
        id: 'copy-relative-path',
        label: 'Copy Relative Path',
        icon: Link
      },
      {
        id: 'reveal',
        label: 'Reveal in Explorer',
        icon: FolderOpen
      }
    )

    // Separator
    items.push({ id: 'separator-3' as ContextMenuAction, label: '', separator: true })

    // Folder-specific actions
    items.push(
      {
        id: 'open-terminal',
        label: 'Open in Terminal',
        icon: Terminal
      },
      {
        id: 'compress',
        label: 'Add to Archive...',
        icon: Archive
      }
    )

    // Advanced actions
    items.push({ id: 'separator-4' as ContextMenuAction, label: '', separator: true })
    items.push(
      {
        id: 'properties',
        label: 'Properties',
        icon: Settings
      }
    )

    return items
  }, [canPaste])

  const handleMenuItemClick = useCallback((action: ContextMenuAction) => {
    onAction(action, item)
    onClose()
  }, [onAction, item, onClose])

  const handleKeyDown = useCallback((e: React.KeyboardEvent) => {
    if (e.key === 'Escape') {
      onClose()
    }
  }, [onClose])

  if (!visible) return null

  return (
    <div
      className="fixed inset-0 z-50"
      onClick={onClose}
      onContextMenu={(e) => e.preventDefault()}
    >
      <div
        className="absolute bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-700 rounded-md shadow-lg py-1 min-w-48"
        style={{
          left: position.x,
          top: position.y,
          zIndex: 1000
        }}
        onClick={(e) => e.stopPropagation()}
        onKeyDown={handleKeyDown}
        tabIndex={-1}
      >
        {menuItems.map((menuItem, index) => {
          if (menuItem.separator) {
            return (
              <div
                key={`separator-${index}`}
                className="h-px bg-gray-200 dark:bg-gray-700 my-1"
              />
            )
          }

          const IconComponent = menuItem.icon
          const isDisabled = menuItem.disabled

          return (
            <button
              key={menuItem.id}
              className={`
                w-full px-3 py-2 text-left text-sm flex items-center gap-2 hover:bg-gray-100 dark:hover:bg-gray-700
                ${isDisabled ? 'opacity-50 cursor-not-allowed' : 'cursor-pointer'}
                ${menuItem.dangerous ? 'text-red-600 dark:text-red-400 hover:bg-red-50 dark:hover:bg-red-900/20' : ''}
              `}
              onClick={() => !isDisabled && handleMenuItemClick(menuItem.id)}
              disabled={isDisabled}
            >
              {IconComponent && (
                <IconComponent className="h-4 w-4 flex-shrink-0" />
              )}
              <span className="flex-1">{menuItem.label}</span>
              {menuItem.shortcut && (
                <span className="text-xs text-gray-500 dark:text-gray-400">
                  {menuItem.shortcut}
                </span>
              )}
            </button>
          )
        })}
      </div>
    </div>
  )
}

/**
 * Context menu wrapper component for folders
 */
export const FolderContextMenuWrapper: React.FC<{
  children: React.ReactNode
  item: FileSystemItem
  onAction: (action: ContextMenuAction, item: FileSystemItem) => void
}> = ({ children, item, onAction }) => {
  const [contextMenu, setContextMenu] = React.useState<{
    visible: boolean
    position: { x: number; y: number }
  }>({
    visible: false,
    position: { x: 0, y: 0 }
  })

  const handleContextMenu = useCallback((e: React.MouseEvent) => {
    e.preventDefault()
    e.stopPropagation()
    
    setContextMenu({
      visible: true,
      position: { x: e.clientX, y: e.clientY }
    })
  }, [])

  const handleClose = useCallback(() => {
    setContextMenu(prev => ({ ...prev, visible: false }))
  }, [])

  const handleAction = useCallback((action: ContextMenuAction, folderItem: FileSystemItem) => {
    onAction(action, folderItem)
    handleClose()
  }, [onAction, handleClose])

  return (
    <>
      <div onContextMenu={handleContextMenu}>
        {children}
      </div>
      
      <FolderContextMenu
        item={item}
        position={contextMenu.position}
        onAction={handleAction}
        onClose={handleClose}
        visible={contextMenu.visible}
      />
    </>
  )
}

export default FolderContextMenu
