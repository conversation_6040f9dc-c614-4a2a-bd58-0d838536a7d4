/**
 * Context Menu Types and Interfaces
 * Defines types for the context menu system
 */

import { FileSystemItem } from '../types'

export type ContextMenuAction = 
  // File actions
  | 'open' 
  | 'open-with' 
  | 'copy' 
  | 'cut' 
  | 'paste' 
  | 'rename' 
  | 'delete' 
  | 'duplicate'
  | 'copy-path' 
  | 'copy-relative-path' 
  | 'reveal' 
  | 'properties'
  // Folder actions
  | 'new-file' 
  | 'new-folder' 
  | 'open-terminal'
  // Advanced actions
  | 'compress'
  | 'extract'
  | 'git-add'
  | 'git-commit'

export interface ContextMenuItem {
  id: ContextMenuAction
  label: string
  icon?: React.ComponentType<any>
  shortcut?: string
  disabled?: boolean
  separator?: boolean
  submenu?: ContextMenuItem[]
  dangerous?: boolean // For delete, etc.
}

export interface ContextMenuPosition {
  x: number
  y: number
}

export interface ContextMenuProps {
  item: FileSystemItem
  position: ContextMenuPosition
  onAction: (action: ContextMenuAction, item: FileSystemItem) => void
  onClose: () => void
  visible: boolean
}

export interface ClipboardItem {
  type: 'file' | 'folder'
  path: string
  name: string
  operation: 'copy' | 'cut'
  timestamp: number
}

export interface FileOperationResult {
  success: boolean
  error?: string
  data?: any
}

export interface ContextMenuActionHandler {
  canExecute(action: ContextMenuAction, item: FileSystemItem): boolean
  execute(action: ContextMenuAction, item: FileSystemItem): Promise<FileOperationResult>
}

export interface ContextMenuConfig {
  enableAdvancedActions: boolean
  enableGitActions: boolean
  enableTerminalActions: boolean
  enableCompressionActions: boolean
  showIcons: boolean
  showShortcuts: boolean
}

export interface ContextMenuState {
  visible: boolean
  position: ContextMenuPosition
  item: FileSystemItem | null
  submenuOpen: string | null
}

// Predefined menu item configurations
export const COMMON_FILE_ACTIONS: ContextMenuItem[] = [
  {
    id: 'open',
    label: 'Open',
    shortcut: 'Enter'
  },
  {
    id: 'open-with',
    label: 'Open With...'
  }
]

export const EDIT_ACTIONS: ContextMenuItem[] = [
  {
    id: 'copy',
    label: 'Copy',
    shortcut: 'Ctrl+C'
  },
  {
    id: 'cut',
    label: 'Cut',
    shortcut: 'Ctrl+X'
  },
  {
    id: 'paste',
    label: 'Paste',
    shortcut: 'Ctrl+V'
  },
  {
    id: 'rename',
    label: 'Rename',
    shortcut: 'F2'
  },
  {
    id: 'delete',
    label: 'Delete',
    shortcut: 'Delete',
    dangerous: true
  }
]

export const PATH_ACTIONS: ContextMenuItem[] = [
  {
    id: 'copy-path',
    label: 'Copy Path'
  },
  {
    id: 'copy-relative-path',
    label: 'Copy Relative Path'
  },
  {
    id: 'reveal',
    label: 'Reveal in Explorer'
  }
]

export const FOLDER_CREATION_ACTIONS: ContextMenuItem[] = [
  {
    id: 'new-file',
    label: 'New File'
  },
  {
    id: 'new-folder',
    label: 'New Folder'
  }
]

export const ADVANCED_ACTIONS: ContextMenuItem[] = [
  {
    id: 'properties',
    label: 'Properties'
  },
  {
    id: 'open-terminal',
    label: 'Open in Terminal'
  }
]
