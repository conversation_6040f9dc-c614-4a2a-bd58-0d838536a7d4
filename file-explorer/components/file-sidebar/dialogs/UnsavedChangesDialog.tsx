/**
 * Unsaved Changes Dialog Component
 * Handles confirmation when switching projects with unsaved changes
 */

import { Save } from "lucide-react"
import { But<PERSON> } from "@/components/ui/button"
import { UnsavedChangesDialogProps } from "../types"
import { useDialog } from "@/components/dialogs"

export const UnsavedChangesDialog = ({
  onConfirm
}: Omit<UnsavedChangesDialogProps, 'open' | 'onOpenChange'>) => {
  const { openDialog, closeDialog } = useDialog()

  const openUnsavedChangesDialog = () => {
    openDialog('unsaved-changes', <UnsavedChangesDialogContent
      onConfirm={(save) => {
        onConfirm(save)
        closeDialog('unsaved-changes')
      }}
      onClose={() => closeDialog('unsaved-changes')}
    />, {
      size: 'sm',
      closable: true,
      position: 'center'
    })
  }

  return (
    <Button onClick={openUnsavedChangesDialog}>
      Show Unsaved Changes Dialog
    </Button>
  )
}

interface UnsavedChangesDialogContentProps {
  onConfirm: (save: boolean) => void
  onClose: () => void
}

function UnsavedChangesDialogContent({
  onConfirm,
  onClose
}: UnsavedChangesDialogContentProps) {
  return (
    <div className="sm:max-w-[425px] bg-background rounded-lg shadow-lg p-6">
      <div className="mb-6">
        <h2 className="text-lg font-semibold">Unsaved Changes</h2>
      </div>
      <div className="py-4">
        <p className="text-sm text-muted-foreground">
          You have unsaved changes in the editor. Would you like to save them before switching projects?
        </p>
      </div>
      <div className="flex justify-end gap-2 mt-6">
        <Button
          type="button"
          variant="outline"
          onClick={() => onConfirm(false)}
        >
          Don't Save
        </Button>
        <Button
          type="button"
          variant="outline"
          onClick={onClose}
        >
          Cancel
        </Button>
        <Button
          type="button"
          onClick={() => onConfirm(true)}
        >
          <Save className="h-4 w-4 mr-2" />
          Save & Switch
        </Button>
      </div>
    </div>
  )
}
