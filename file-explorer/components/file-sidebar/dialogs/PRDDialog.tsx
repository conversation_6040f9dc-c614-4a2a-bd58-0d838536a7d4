/**
 * PRD Dialog Component
 * Handles PRD upload and validation during project creation
 */

import { Button } from "@/components/ui/button"
import { PRDUploadUI } from "../../intake/prd-upload-ui"
import { PRDDialogProps } from "../types"
import { useDialog } from "@/components/dialogs"

export const PRDDialog = ({
  currentProjectPath,
  prdValidated,
  onPRDUploaded,
  onPRDParsed,
  onValidationChange,
  onCancel
}: Omit<PRDDialogProps, 'open' | 'onOpenChange'>) => {
  const { openDialog, closeDialog } = useDialog()

  const openPRDDialog = () => {
    openDialog('prd-upload', <PRDDialogContent
      currentProjectPath={currentProjectPath}
      prdValidated={prdValidated}
      onPRDUploaded={onPRDUploaded}
      onPRDParsed={onPRDParsed}
      onValidationChange={onValidationChange}
      onCancel={() => {
        onCancel()
        closeDialog('prd-upload')
      }}
      onClose={() => closeDialog('prd-upload')}
    />, {
      size: 'lg',
      closable: true,
      position: 'center'
    })
  }

  return (
    <Button onClick={openPRDDialog}>
      Upload PRD
    </Button>
  )
}

interface PRDDialogContentProps {
  currentProjectPath: string | null
  prdValidated: boolean
  onPRDUploaded: (filePath: string, validation: any, content?: string) => void
  onPRDParsed: (result: any) => void
  onValidationChange: (isValid: boolean) => void
  onCancel: () => void
  onClose: () => void
}

function PRDDialogContent({
  currentProjectPath,
  prdValidated,
  onPRDUploaded,
  onPRDParsed,
  onValidationChange,
  onCancel,
  onClose
}: PRDDialogContentProps) {
  return (
    <div className="sm:max-w-[800px] overflow-y-auto bg-background rounded-lg shadow-lg p-6">
      <div className="mb-6">
        <h2 className="text-lg font-semibold">Upload Project Requirements Document (PRD)</h2>
        <p className="text-sm text-muted-foreground">
          A valid PRD is required before creating the project. This ensures proper task orchestration and project structure.
        </p>
      </div>
      <div className="py-4">
        <PRDUploadUI
          onPRDUploaded={onPRDUploaded}
          onPRDParsed={onPRDParsed}
          onValidationChange={onValidationChange}
          projectPath={currentProjectPath || undefined}
          className="max-h-[60vh] overflow-y-auto"
        />
      </div>
      <div className="flex justify-between items-center mt-6">
        <Button
          type="button"
          variant="outline"
          onClick={onCancel}
        >
          Cancel Project Creation
        </Button>
        <div className="text-sm text-muted-foreground">
          {prdValidated ?
            "✅ PRD validated. Click 'Parse PRD with Taskmaster' to continue." :
            "❌ Please upload and validate a PRD first."
          }
        </div>
      </div>
    </div>
  )
}
