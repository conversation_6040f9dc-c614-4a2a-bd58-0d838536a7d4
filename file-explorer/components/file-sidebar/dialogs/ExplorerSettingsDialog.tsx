/**
 * Explorer Settings Dialog Component
 * Handles file explorer display and behavior settings
 */

import { Button } from "@/components/ui/button"
import { Switch } from "@/components/ui/switch"
import { Label } from "@/components/ui/label"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { ExplorerSettingsDialogProps } from "../types"
import { useDialog } from "@/components/dialogs"

export const ExplorerSettingsDialog = ({
  settings,
  onSettingsChange,
  onSave
}: Omit<ExplorerSettingsDialogProps, 'open' | 'onOpenChange'>) => {
  const { openDialog, closeDialog } = useDialog()

  const openExplorerSettingsDialog = () => {
    openDialog('explorer-settings', <ExplorerSettingsDialogContent
      settings={settings}
      onSettingsChange={onSettingsChange}
      onSave={() => {
        onSave()
        closeDialog('explorer-settings')
      }}
      onClose={() => closeDialog('explorer-settings')}
    />, {
      size: 'md',
      closable: true,
      position: 'center'
    })
  }

  return (
    <Button onClick={openExplorerSettingsDialog}>
      Explorer Settings
    </Button>
  )
}

interface ExplorerSettingsDialogContentProps {
  settings: any
  onSettingsChange: (settings: any) => void
  onSave: () => void
  onClose: () => void
}

function ExplorerSettingsDialogContent({
  settings,
  onSettingsChange,
  onSave,
  onClose
}: ExplorerSettingsDialogContentProps) {
  return (
    <div className="sm:max-w-[500px] bg-background rounded-lg shadow-lg p-6">
      <div className="mb-6">
        <h2 className="text-lg font-semibold">Explorer Settings</h2>
        <p className="text-sm text-muted-foreground">
          Configure how files and folders are displayed in the Explorer
        </p>
      </div>
        <div className="py-4">
          <Card>
            <CardHeader>
              <CardTitle className="text-base">Display Options</CardTitle>
              <CardDescription>Control what is shown in the file tree</CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="flex items-center justify-between">
                <Label htmlFor="show-hidden">Show Hidden Files</Label>
                <Switch
                  id="show-hidden"
                  checked={settings.showHiddenFiles}
                  onCheckedChange={(checked) =>
                    onSettingsChange({ showHiddenFiles: checked })
                  }
                />
              </div>

              <div className="flex items-center justify-between">
                <Label htmlFor="show-extensions">Show File Extensions</Label>
                <Switch
                  id="show-extensions"
                  checked={settings.showFileExtensions}
                  onCheckedChange={(checked) =>
                    onSettingsChange({ showFileExtensions: checked })
                  }
                />
              </div>

              <div className="flex items-center justify-between">
                <Label htmlFor="show-icons">Show File Icons</Label>
                <Switch
                  id="show-icons"
                  checked={settings.showFileIcons}
                  onCheckedChange={(checked) =>
                    onSettingsChange({ showFileIcons: checked })
                  }
                />
              </div>

              <div className="flex items-center justify-between">
                <Label htmlFor="auto-expand">Auto Expand Folders</Label>
                <Switch
                  id="auto-expand"
                  checked={settings.autoExpandFolders}
                  onCheckedChange={(checked) =>
                    onSettingsChange({ autoExpandFolders: checked })
                  }
                />
              </div>

              <div className="flex items-center justify-between">
                <Label htmlFor="compact-view">Compact View</Label>
                <Switch
                  id="compact-view"
                  checked={settings.compactView}
                  onCheckedChange={(checked) =>
                    onSettingsChange({ compactView: checked })
                  }
                />
              </div>
            </CardContent>
          </Card>
        </div>
        <div className="flex justify-end gap-2 mt-6">
          <Button
            type="button"
            variant="outline"
            onClick={onClose}
          >
            Close
          </Button>
          <Button
            type="button"
            onClick={onSave}
          >
            Save Settings
          </Button>
        </div>
    </div>
  )
}
