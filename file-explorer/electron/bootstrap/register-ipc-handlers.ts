/**
 * IPC Handlers Registration - Inter-Process Communication Setup
 * 
 * Extracted from main.ts to handle IPC channel setup and window management.
 */

import { ipcMain, dialog, BrowserWindow } from 'electron';
import fs from 'fs';
import path from 'path';
import crypto from 'crypto';
import os from 'os';
import { spawn } from 'node-pty';
import { TERMINAL_CONFIG, getShellArgs, getEnhancedEnv } from './main-constants';
import { getMainWindow } from './create-main-window';

// ✅ Safe console for logging
const safeConsole = {
  log: (...args: any[]) => {
    try {
      console.log(...args);
    } catch (error) {
      // Silently ignore write errors
    }
  },
  error: (...args: any[]) => {
    try {
      console.error(...args);
    } catch (error) {
      // Silently ignore write errors
    }
  },
  warn: (...args: any[]) => {
    try {
      console.warn(...args);
    } catch (error) {
      // Silently ignore write errors
    }
  }
};

// ✅ Terminal session storage
const terminals: { [id: string]: any } = {};
const userTerminalSessions = new Map<string, {
  ptyProcess: any;
  shell: string;
  createdAt: number;
}>();

// ✅ CRITICAL FIX: Global file explorer state for synchronization
let globalFileExplorerState: {
  projects?: any[];
  recentProjects?: Array<{name: string, path: string, lastOpened: number}>;
  selectedFile?: any;
  currentProjectPath?: string;
} = {};

/**
 * Register file system IPC handlers
 */
export function registerFileSystemHandlers(): void {
  // Select folder dialog
  ipcMain.handle('select-folder', async () => {
    const mainWindow = getMainWindow();
    if (!mainWindow) return { success: false, error: 'Main window not available' };

    const result = await dialog.showOpenDialog(mainWindow, {
      properties: ['openDirectory'],
      title: 'Select Project Folder'
    });

    if (!result.canceled && result.filePaths.length > 0) {
      const folderPath = result.filePaths[0];
      return {
        success: true,
        path: folderPath,
        name: path.basename(folderPath)
      };
    }

    return { success: false };
  });

  // Read directory
  ipcMain.handle('read-directory', async (event, dirPath: string) => {
    try {
      if (!dirPath || typeof dirPath !== 'string') {
        return { success: false, error: 'Invalid directory path provided' };
      }

      if (!fs.existsSync(dirPath)) {
        return { success: false, error: `Directory does not exist: ${dirPath}` };
      }

      const stats = fs.statSync(dirPath);
      if (!stats.isDirectory()) {
        return { success: false, error: `Path is not a directory: ${dirPath}` };
      }

      const items = fs.readdirSync(dirPath);
      const itemsWithStats = items.map(item => {
        const itemPath = path.join(dirPath, item);
        const itemStats = fs.statSync(itemPath);

        // Map isDirectory to type field that frontend expects
        const isDirectory = itemStats.isDirectory();
        const type = isDirectory ? 'folder' : (path.extname(item).slice(1) || 'file');

        return {
          id: Date.now() + Math.random(), // Add unique ID
          name: item,
          path: itemPath,
          type: type,
          isDirectory: isDirectory, // Keep for backward compatibility
          size: itemStats.size,
          modified: itemStats.mtime,
          expanded: false, // Default for folders
          files: isDirectory ? [] : undefined // Empty array for folders, undefined for files
        };
      });

      return { success: true, items: itemsWithStats };
    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error reading directory'
      };
    }
  });

  // Read file
  ipcMain.handle('read-file', async (event, filePath: string) => {
    try {
      if (!filePath || typeof filePath !== 'string') {
        return { success: false, error: 'Invalid file path provided' };
      }

      if (!fs.existsSync(filePath)) {
        return { success: false, error: `File does not exist: ${filePath}` };
      }

      // Check if path is actually a file (not a directory)
      const stats = fs.statSync(filePath);
      if (!stats.isFile()) {
        return { success: false, error: `Path is not a file: ${filePath}` };
      }

      const content = fs.readFileSync(filePath, 'utf8');
      return { success: true, content };
    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error reading file'
      };
    }
  });

  // Save file
  ipcMain.handle('save-file', async (event, filePath: string, content: string) => {
    try {
      if (!filePath || typeof filePath !== 'string') {
        return { success: false, error: 'Invalid file path provided' };
      }

      // Ensure directory exists
      const dir = path.dirname(filePath);
      if (!fs.existsSync(dir)) {
        fs.mkdirSync(dir, { recursive: true });
      }

      fs.writeFileSync(filePath, content, 'utf8');
      return { success: true };
    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error saving file'
      };
    }
  });

  // Create file
  ipcMain.handle('create-file', async (event, filePath: string, content: string = '') => {
    try {
      if (!filePath || typeof filePath !== 'string') {
        return { success: false, error: 'Invalid file path provided' };
      }

      // Check if file already exists
      if (fs.existsSync(filePath)) {
        return { success: false, error: 'File already exists' };
      }

      // Ensure directory exists
      const dir = path.dirname(filePath);
      if (!fs.existsSync(dir)) {
        fs.mkdirSync(dir, { recursive: true });
      }

      fs.writeFileSync(filePath, content, 'utf8');
      return { success: true };
    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error creating file'
      };
    }
  });

  // Delete file
  ipcMain.handle('delete-file', async (event, filePath: string) => {
    try {
      if (!filePath || typeof filePath !== 'string') {
        return { success: false, error: 'Invalid file path provided' };
      }

      if (!fs.existsSync(filePath)) {
        return { success: false, error: `File does not exist: ${filePath}` };
      }

      fs.unlinkSync(filePath);
      return { success: true };
    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error deleting file'
      };
    }
  });

  // Ensure directory
  ipcMain.handle('ensure-directory', async (event, dirPath: string) => {
    try {
      if (!dirPath || typeof dirPath !== 'string') {
        return { success: false, error: 'Invalid directory path provided' };
      }

      if (!fs.existsSync(dirPath)) {
        fs.mkdirSync(dirPath, { recursive: true });
      }
      return { success: true };
    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error creating directory'
      };
    }
  });

  // Append to file
  ipcMain.handle('append-file', async (event, filePath: string, content: string) => {
    try {
      if (!filePath || typeof filePath !== 'string') {
        return { success: false, error: 'Invalid file path provided' };
      }

      // Ensure directory exists
      const dir = path.dirname(filePath);
      if (!fs.existsSync(dir)) {
        fs.mkdirSync(dir, { recursive: true });
      }

      fs.appendFileSync(filePath, content, 'utf8');
      return { success: true };
    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error appending to file'
      };
    }
  });

  safeConsole.log('✅ File system IPC handlers registered');
}

/**
 * Register file explorer state synchronization handlers
 */
export function registerFileExplorerSyncHandlers(): void {
  // Get current file explorer state
  ipcMain.handle('get-file-explorer-state', async () => {
    try {
      safeConsole.log('📡 Sending file explorer state:', globalFileExplorerState);
      return globalFileExplorerState;
    } catch (error) {
      safeConsole.error('❌ Error getting file explorer state:', error);
      return {};
    }
  });

  // Sync file explorer state across windows
  ipcMain.handle('sync-file-explorer-state', async (event, stateUpdate: Partial<typeof globalFileExplorerState>) => {
    try {
      // Update global state
      globalFileExplorerState = { ...globalFileExplorerState, ...stateUpdate };
      safeConsole.log('📡 Updated global file explorer state:', globalFileExplorerState);

      // Broadcast to all windows except the sender
      const allWindows = BrowserWindow.getAllWindows();
      allWindows.forEach(window => {
        if (window.webContents !== event.sender) {
          window.webContents.send('file-explorer-state-sync', stateUpdate);
        }
      });

      return { success: true };
    } catch (error) {
      safeConsole.error('❌ Error syncing file explorer state:', error);
      return { success: false, error: error instanceof Error ? error.message : 'Unknown error' };
    }
  });

  safeConsole.log('✅ File explorer sync IPC handlers registered');
}

/**
 * Register terminal IPC handlers
 */
export function registerTerminalHandlers(): void {
  // Create terminal
  ipcMain.handle('terminal:create', (event, terminalSettings?: {
    shell?: string;
    cols?: number;
    rows?: number;
  }) => {
    try {
      // Use settings or fallback to defaults
      const shell = terminalSettings?.shell || TERMINAL_CONFIG.defaultShell;
      const cols = terminalSettings?.cols || TERMINAL_CONFIG.defaultCols;
      const rows = terminalSettings?.rows || TERMINAL_CONFIG.defaultRows;

      // Generate unique terminal ID
      const terminalId = crypto.randomUUID();

      // Create PTY process
      const ptyProcess = spawn(shell, getShellArgs(shell), {
        name: 'xterm-256color',
        cols,
        rows,
        cwd: process.env.HOME || process.cwd(),
        env: getEnhancedEnv(),
        encoding: 'utf8',
      });

      // Store terminal reference
      terminals[terminalId] = ptyProcess;

      safeConsole.log(`✅ Terminal created: ${terminalId} (shell: ${shell})`);

      // Return just the terminalId as expected by frontend
      return terminalId;
    } catch (error) {
      safeConsole.error(`❌ Terminal creation error:`, error);
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error'
      };
    }
  });

  // Terminal resize
  ipcMain.handle('terminal:resize', (event, { id, cols, rows }: { id: string; cols: number; rows: number }) => {
    try {
      const terminal = terminals[id];
      if (!terminal) {
        return { success: false, error: `Terminal not found: ${id}` };
      }

      terminal.resize(cols, rows);
      safeConsole.log(`✅ Terminal ${id} resized to ${cols}x${rows}`);

      return { success: true };
    } catch (error) {
      safeConsole.error(`❌ Terminal resize error:`, error);
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error'
      };
    }
  });

  // Terminal input
  ipcMain.on('terminal:input', (event, { id, data }: { id: string; data: string }) => {
    try {
      const terminal = terminals[id];
      if (!terminal) {
        safeConsole.error(`❌ Terminal not found for input: ${id}`);
        return;
      }

      terminal.write(data);
      safeConsole.log(`✅ Terminal ${id} input: ${data.replace(/\r?\n/g, '\\n')}`);
    } catch (error) {
      safeConsole.error(`❌ Terminal input error:`, error);
    }
  });

  // Terminal listen
  ipcMain.on('terminal:listen', (event, id) => {
    try {
      const terminal = terminals[id];
      if (!terminal) {
        safeConsole.error(`❌ Terminal not found for listening: ${id}`);
        return;
      }

      terminal.onData((data: string) => {
        event.sender.send(`terminal:data:${id}`, data);
      });

      terminal.onExit((e: { exitCode: number; signal?: number }) => {
        safeConsole.log(`✅ Terminal ${id} exited with code: ${e.exitCode}, signal: ${e.signal}`);
        event.sender.send(`terminal:exit:${id}`, { exitCode: e.exitCode, signal: e.signal });
        delete terminals[id];
      });

      safeConsole.log(`✅ Terminal ${id} listeners attached`);
    } catch (error) {
      safeConsole.error(`❌ Terminal listen error:`, error);
    }
  });

  // Terminal dispose
  ipcMain.on('terminal:dispose', (event, id) => {
    try {
      const terminal = terminals[id];
      if (!terminal) {
        safeConsole.error(`❌ Terminal not found for disposal: ${id}`);
        return;
      }

      terminal.kill();
      delete terminals[id];
      safeConsole.log(`✅ Terminal ${id} disposed`);
    } catch (error) {
      safeConsole.error(`❌ Terminal disposal error:`, error);
    }
  });

  safeConsole.log('✅ Terminal IPC handlers registered');
}

/**
 * Get terminal sessions for cleanup
 */
export function getTerminalSessions() {
  return { terminals, userTerminalSessions };
}

/**
 * Register user terminal session handlers
 */
export function registerUserTerminalHandlers(): void {
  // Create user session
  ipcMain.handle('terminal:create-user-session', (event, shell = '/bin/bash') => {
    try {
      const sessionId = crypto.randomUUID();
      const effectiveShell = shell || TERMINAL_CONFIG.defaultShell;

      const ptyProcess = spawn(effectiveShell, getShellArgs(effectiveShell), {
        name: 'xterm-256color',
        cols: 80,
        rows: 24,
        cwd: process.env.HOME || process.cwd(),
        env: getEnhancedEnv(),
        encoding: 'utf8',
      });

      userTerminalSessions.set(sessionId, {
        ptyProcess,
        shell: effectiveShell,
        createdAt: Date.now()
      });

      // Set up data listener
      ptyProcess.onData((data: string) => {
        event.sender.send(`terminal:user-session-data:${sessionId}`, data);
      });

      // Set up exit listener
      ptyProcess.onExit((e: { exitCode: number; signal?: number }) => {
        safeConsole.log(`✅ User terminal session ${sessionId} exited with code: ${e.exitCode}`);
        event.sender.send(`terminal:user-session-exit:${sessionId}`, { exitCode: e.exitCode, signal: e.signal });
        userTerminalSessions.delete(sessionId);
      });

      safeConsole.log(`✅ User terminal session created: ${sessionId} (shell: ${effectiveShell})`);

      return {
        success: true,
        sessionId,
        shell: effectiveShell
      };
    } catch (error) {
      safeConsole.error(`❌ User terminal session creation error:`, error);
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error'
      };
    }
  });

  // Dispose user session
  ipcMain.handle('terminal:dispose-user-session', (event, sessionId: string) => {
    try {
      const session = userTerminalSessions.get(sessionId);
      if (!session) {
        return { success: false, error: `Session not found: ${sessionId}` };
      }

      session.ptyProcess.kill();
      userTerminalSessions.delete(sessionId);
      safeConsole.log(`✅ User terminal session disposed: ${sessionId}`);

      return { success: true };
    } catch (error) {
      safeConsole.error(`❌ User terminal session disposal error:`, error);
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error'
      };
    }
  });

  // Write to user session
  ipcMain.handle('terminal:write-user-session', (event, sessionId: string, data: string) => {
    try {
      const session = userTerminalSessions.get(sessionId);
      if (!session) {
        return { success: false, error: `Session not found: ${sessionId}` };
      }

      session.ptyProcess.write(data);
      return { success: true };
    } catch (error) {
      safeConsole.error(`❌ User terminal session write error:`, error);
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error'
      };
    }
  });

  // Resize user session
  ipcMain.handle('terminal:resize-user-session', (event, sessionId: string, cols: number, rows: number) => {
    try {
      const session = userTerminalSessions.get(sessionId);
      if (!session) {
        return { success: false, error: `Session not found: ${sessionId}` };
      }

      session.ptyProcess.resize(cols, rows);
      return { success: true };
    } catch (error) {
      safeConsole.error(`❌ User terminal session resize error:`, error);
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error'
      };
    }
  });

  // List user sessions
  ipcMain.handle('terminal:list-user-sessions', () => {
    try {
      const sessions = Array.from(userTerminalSessions.entries()).map(([sessionId, session]) => ({
        sessionId,
        shell: session.shell,
        createdAt: session.createdAt
      }));

      return { success: true, sessions };
    } catch (error) {
      safeConsole.error(`❌ User terminal session list error:`, error);
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error'
      };
    }
  });

  safeConsole.log('✅ User terminal session IPC handlers registered');
}

/**
 * Register agent command handlers
 */
export function registerAgentCommandHandlers(): void {
  // Agent command execution
  ipcMain.handle('terminal:agent-command', async (event, { command, agentId, sessionId, timeout, workingDirectory, environment }: {
    command: string;
    agentId: string;
    sessionId?: string;
    timeout?: number;
    workingDirectory?: string;
    environment?: Record<string, string>;
  }) => {
    try {
      const startTime = Date.now();
      const effectiveTimeout = timeout || 30000;
      const effectiveWorkingDirectory = workingDirectory || process.cwd();
      const effectiveSessionId = sessionId || crypto.randomUUID();

      // Create PTY process for command execution
      const shell = TERMINAL_CONFIG.defaultShell;
      const ptyProcess = spawn(shell, [], {
        name: 'xterm-256color',
        cols: 80,
        rows: 30,
        cwd: effectiveWorkingDirectory,
        env: getEnhancedEnv(environment),
        encoding: 'utf8',
      });

      let output = '';
      let hasExited = false;
      let exitCode = 0;

      // Set up data listener
      ptyProcess.onData((data: string) => {
        output += data;
      });

      // Set up exit listener
      ptyProcess.onExit((e: { exitCode: number; signal?: number }) => {
        hasExited = true;
        exitCode = e.exitCode;
      });

      // Send command to PTY
      ptyProcess.write(command + '\n');

      // Wait for command completion or timeout
      const waitForCompletion = () => {
        return new Promise<void>((resolve) => {
          const checkInterval = setInterval(() => {
            if (hasExited || Date.now() - startTime > effectiveTimeout) {
              clearInterval(checkInterval);
              resolve();
            }
          }, 100);
        });
      };

      await waitForCompletion();

      // Clean up PTY process
      if (!hasExited) {
        ptyProcess.kill();
      }

      const executionTime = Date.now() - startTime;
      const success = exitCode === 0 && !output.includes('command not found') && !output.includes('error');

      safeConsole.log(`✅ Agent command executed: ${command} (agent: ${agentId}, time: ${executionTime}ms, success: ${success})`);

      return {
        success,
        output: output.trim(),
        exitCode,
        executionTime,
        sessionId: effectiveSessionId,
        agentId,
        command
      };
    } catch (error) {
      safeConsole.error(`❌ Agent command error:`, error);
      return {
        success: false,
        output: '',
        error: error instanceof Error ? error.message : 'Unknown error',
        exitCode: -1,
        executionTime: 0,
        sessionId: sessionId || crypto.randomUUID(),
        agentId,
        command
      };
    }
  });

  safeConsole.log('✅ Agent command IPC handlers registered');
}

/**
 * Register all IPC handlers
 */
export function registerAllIPCHandlers(): void {
  registerFileSystemHandlers();
  registerTerminalHandlers();
  registerUserTerminalHandlers();
  registerAgentCommandHandlers();
  registerFileExplorerSyncHandlers(); // ✅ CRITICAL FIX: Add file explorer sync handlers

  // ✅ CRITICAL FIX: Import and register window handlers
  const { registerWindowIPCHandlers } = require('./register-window-handlers');
  registerWindowIPCHandlers();

  safeConsole.log('✅ All IPC handlers registered');
}

/**
 * Cleanup all terminal sessions
 */
export function cleanupTerminalSessions(): void {
  safeConsole.log('🧹 Cleaning up terminal sessions...');

  // Clean up regular terminals
  Object.keys(terminals).forEach(id => {
    try {
      terminals[id].kill();
      delete terminals[id];
    } catch (error) {
      safeConsole.error(`Error cleaning up terminal ${id}:`, error);
    }
  });

  // Clean up user terminal sessions
  userTerminalSessions.forEach((session, sessionId) => {
    try {
      session.ptyProcess.kill();
    } catch (error) {
      safeConsole.error(`Error cleaning up user session ${sessionId}:`, error);
    }
  });
  userTerminalSessions.clear();

  safeConsole.log('✅ Terminal cleanup completed');
}
